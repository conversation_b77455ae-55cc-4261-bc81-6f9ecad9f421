# 储能系统容量测算完整解决方案

## 概述

本项目实现了基于Excel数据的储能系统容量测算完整解决方案，包含四个核心计算模块和一个集成框架，能够处理负荷原始清单和电价数据，生成三个主要输出表。

## 系统架构

### 核心模块

1. **储能装机容量测算.py** - 计算变压器容量、功率因素、系统容量等基础参数
2. **基本电费设定.py** - 计算月度电费、需量缴费、按容缴费等费用参数
3. **容量负荷测算.py** - 实现31步复杂计算逻辑，包括充放电循环、DOD统计等
4. **汇总指标.py** - 计算年度汇总指标，包括总电量、总收益、循环统计等

### 集成框架

- **储能系统集成计算.py** - 整合所有模块的主要接口
- **test_complete_system.py** - 完整的测试框架

## 主要功能

### 数据处理能力
- 支持从Excel文件读取负荷原始清单和电价数据
- 支持从数据平台接口接收数据
- 自动数据清洗和验证

### 计算功能
- 31步详细计算逻辑实现
- 96个时间点/天的15分钟间隔数据处理
- 多种电价策略支持（特殊/正常电价）
- DOD统计和循环分析

### 输出功能
- 三个标准输出表生成
- Excel格式结果导出
- 详细计算报告生成

## 快速开始

### 安装依赖

```bash
pip install pandas openpyxl
```

### 基本使用

```python
from 储能系统集成计算 import main_calculation_pipeline

# 从Excel文件计算
results = main_calculation_pipeline(
    file_path="data/(厂商机密)储能容量测算模型.xlsx",
    output_file="计算结果.xlsx"
)
```

### 自定义参数

```python
config_params = {
    'system_capacity_kw': 200,      # 系统容量(kW)
    'system_energy_kwh': 400,       # 系统电量(kWh)
    'transformer_capacity_kva': 1000, # 变压器容量(kVA)
    'power_factor': 0.92,           # 功率因素
    'dod_percentage': 0.95,         # DOD百分比
    'daily_decay_rate': 0.000082,   # 日衰减率
    'peak1_discharge_rate': 0.5,    # 峰1放电倍率
}

results = main_calculation_pipeline(
    file_path="data/input.xlsx",
    config_params=config_params,
    output_file="results.xlsx"
)
```

### 数据平台接口

```python
from 储能系统集成计算 import EnergyStorageSystemCalculator
import pandas as pd

# 准备数据
load_data = pd.DataFrame(...)  # 负荷数据
price_data = pd.DataFrame(...) # 电价数据

# 计算
calculator = EnergyStorageSystemCalculator()
results = calculator.calculate_from_data_platform(
    load_data, price_data, config_params
)
```

## 输入数据格式

### 负荷原始清单表
- **用户名称**: 企业名称
- **日期**: 数据日期 (YYYY-MM-DD格式)
- **时间**: 时间点 (HH:MM:SS格式)
- **瞬时有功**: 瞬时有功功率值 (kW)

### 基础参数电价表
- **用电分类**: 大工业用电等
- **电压等级**: 10kV等
- **各时段电价**: 尖峰、高峰、平段、低谷电价
- **月度电价浮动**: 各月份的电价调整

## 输出结果

### 三个主要输出表

1. **装机容量和基本电费汇总表**
   - 变压器容量、系统容量、功率因素等基础参数
   - 月度电费、年度缴费、负载率等费用指标

2. **每日详细计算结果表**
   - 每日31个计算指标
   - 充放电循环数据
   - 日收益计算结果

3. **年度汇总指标表**
   - 全年电量统计
   - 循环次数统计
   - DOD分布统计
   - 总收益和平均收益

### 计算指标说明

#### 基础指标 (1-6)
- 功率上限、尖最小功率、充电容量、系统留存
- 处理后96点瞬时功率、时段平均值

#### 可充电量和可消纳电量 (7-13)
- 谷1/谷2可充电量
- 尖1/尖2可消纳电量
- 峰1/峰2可消纳电量

#### 循环计算 (14-27)
- 谷1/谷2充电量
- 峰1/峰2、尖1/尖2放电量
- 循环1/循环2计算
- 循环计数和两充两放判断

#### 收益和统计 (28-31)
- 尖放电、总放电、总收益
- 满容量天数

## 测试

### 运行完整测试

```bash
python test_complete_system.py
```

### 测试内容
- 数据加载测试
- 各模块单元测试
- 集成测试
- 性能测试
- 数据验证测试

## 配置参数

### 系统参数
- `system_capacity_kw`: 系统容量(kW)，默认200
- `system_energy_kwh`: 系统电量(kWh)，默认400
- `transformer_capacity_kva`: 变压器容量(kVA)，默认2600

### 运行参数
- `dod_percentage`: DOD百分比，默认0.95
- `daily_decay_rate`: 日衰减率，默认0.000082
- `peak1_discharge_rate`: 峰1放电倍率，默认0.5

### 电费参数
- `basic_fee_type`: 基本电费类型，默认'实际'
- `power_factor`: 功率因素，默认0.92
- `demand_price`: 需量电价，默认43.2元/kW
- `capacity_price`: 按容电价，默认30.0元/kVA

## 性能特点

- **高效处理**: 支持大量时间序列数据处理
- **模块化设计**: 各模块独立，便于维护和扩展
- **参数化配置**: 支持灵活的参数调整
- **完整验证**: 包含数据一致性检查
- **错误处理**: 完善的异常处理机制

## 注意事项

1. **数据格式**: 确保Excel文件包含必要的工作表和列
2. **时间格式**: 时间数据应为15分钟间隔，每天96个点
3. **数值范围**: 功率和电量数据应在合理范围内
4. **电价分类**: 注意区分特殊和正常电价的计算逻辑

## 扩展开发

### 添加新的计算逻辑
1. 在相应模块中添加新方法
2. 更新集成计算器的调用逻辑
3. 添加相应的测试用例

### 支持新的数据源
1. 在数据处理模块中添加新的读取方法
2. 确保数据格式标准化
3. 更新文档说明

## 技术支持

如有问题或建议，请检查：
1. 输入数据格式是否正确
2. 参数设置是否合理
3. 系统环境是否满足要求
4. 查看详细的错误日志

本系统设计为高度可配置和可扩展，可根据具体业务需求调整计算逻辑和参数设置。
