# 储能系统计算参数配置说明

## 概述

本文档详细说明了储能系统计算中需要手动配置的参数，这些参数无法从输入数据源直接获取，需要根据具体项目情况进行设定。

## 必需手动配置参数

### 1. 系统容量参数

#### `system_capacity_kw` (系统容量)
- **参数名称**: 系统容量
- **数据类型**: float
- **单位**: kW
- **默认值**: 200
- **说明**: 储能系统的功率容量，根据逻辑规范需要手动填写
- **取值范围**: > 0
- **影响**: 直接影响所有功率相关计算，是核心参数之一

#### `system_energy_kwh` (系统电量)
- **参数名称**: 系统电量
- **数据类型**: float
- **单位**: kWh
- **默认值**: 400 (系统容量的2倍)
- **说明**: 储能系统的电量容量，按照逻辑规范等于系统容量的2倍
- **计算公式**: `system_energy_kwh = system_capacity_kw * 2`
- **影响**: 影响充电容量、系统留存等所有电量相关计算

### 2. 电网参数

#### `transformer_capacity_kva` (变压器容量)
- **参数名称**: 变压器容量
- **数据类型**: float
- **单位**: kVA
- **默认值**: 1000
- **说明**: 企业变压器的额定容量，数据中台可获取但需要配置
- **取值范围**: > 0
- **影响**: 影响功率上限、基本电费计算

#### `power_factor` (功率因素)
- **参数名称**: 功率因素
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.92
- **说明**: 电力系统功率因素，数据中台可获取但需要配置
- **取值范围**: 0 < power_factor ≤ 1
- **影响**: 影响拟定需量、功率上限计算

#### `basic_fee_type` (基本电费类型)
- **参数名称**: 基本电费类型
- **数据类型**: str
- **可选值**: '按容', '按需', '实际'
- **默认值**: '实际'
- **说明**: 企业的基本电费计费方式，数据中台可获取但需要配置
- **影响**: 影响拟定需量计算逻辑

### 3. 运行参数

#### `dod_percentage` (DOD百分比)
- **参数名称**: DOD百分比
- **数据类型**: float
- **单位**: 无量纲 (0-1)
- **默认值**: 0.95
- **说明**: 放电深度(Depth of Discharge)，影响系统留存计算
- **取值范围**: 0 < dod_percentage ≤ 1
- **计算公式**: `系统留存 = 充电容量 * (1 - dod_percentage)`
- **影响**: 直接影响系统留存和所有后续循环计算

#### `daily_decay_rate` (日衰减率)
- **参数名称**: 日衰减率
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.000082
- **说明**: 储能系统每日的容量衰减率
- **取值范围**: 0 ≤ daily_decay_rate < 1
- **计算公式**: `充电容量 = 前一日系统电量 - 初始系统电量 * 日衰减率`
- **影响**: 影响每日充电容量计算

#### `peak1_discharge_rate` (峰1放电倍率)
- **参数名称**: 峰1放电倍率
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.5
- **说明**: 峰1时段的放电倍率系数
- **取值范围**: 0 < peak1_discharge_rate ≤ 1
- **计算公式**: `峰1可消纳电量 = 处理后8:00-8:45功率/4/0.5*峰1放电倍率`
- **影响**: 影响峰1可消纳电量计算

### 4. 电价参数

#### `demand_price` (需量电价)
- **参数名称**: 需量电价
- **数据类型**: float
- **单位**: 元/kW
- **默认值**: 43.2
- **说明**: 按需量计费的电价
- **取值范围**: > 0
- **影响**: 影响需量缴费计算

#### `capacity_price` (按容电价)
- **参数名称**: 按容电价
- **数据类型**: float
- **单位**: 元/kVA
- **默认值**: 30.0
- **说明**: 按容量计费的电价
- **取值范围**: > 0
- **影响**: 影响按容缴费计算

#### `actual_demand_kw` (实际需量)
- **参数名称**: 实际需量
- **数据类型**: float
- **单位**: kW
- **默认值**: 1000
- **说明**: 企业实际需量，数据中台可获取但需要配置
- **取值范围**: > 0
- **影响**: 影响拟定需量计算（按需计费时）

### 5. 电价参数（新增）

#### `sharp_peak_price` (尖峰电价)
- **参数名称**: 尖峰电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 1.29
- **说明**: 尖峰时段的电价，影响尖放电收益计算
- **取值范围**: > 0
- **影响**: 直接影响尖峰时段放电收益

#### `peak_price` (高峰电价)
- **参数名称**: 高峰电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 1.07
- **说明**: 高峰时段的电价，影响峰时段放电收益计算
- **取值范围**: > 0
- **影响**: 直接影响高峰时段放电收益

#### `flat_price` (平段电价)
- **参数名称**: 平段电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.62
- **说明**: 平段时段的电价，影响平时段放电收益计算
- **取值范围**: > 0
- **影响**: 影响平时段放电收益

#### `valley_price` (低谷电价)
- **参数名称**: 低谷电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.26
- **说明**: 低谷时段的电价，影响充电成本计算
- **取值范围**: > 0
- **影响**: 直接影响充电成本和总收益

#### `power_agency_price` (电力代理价)
- **参数名称**: 电力代理价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.05
- **说明**: 电力代理服务费用
- **取值范围**: ≥ 0
- **影响**: 影响总电费计算

#### `transmission_price` (输配电价)
- **参数名称**: 输配电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.15
- **说明**: 输配电网费用
- **取值范围**: ≥ 0
- **影响**: 影响总电费计算

#### `additional_price` (附加费)
- **参数名称**: 附加费
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.02
- **说明**: 其他附加费用
- **取值范围**: ≥ 0
- **影响**: 影响总电费计算

#### `peak_ratio` (高峰比例)
- **参数名称**: 高峰比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.7196
- **说明**: 高峰电价相对于基准电价的比例
- **取值范围**: 0 < peak_ratio ≤ 2
- **影响**: 影响电价计算

#### `sharp_peak_ratio` (尖峰比例)
- **参数名称**: 尖峰比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.2
- **说明**: 尖峰电价相对于基准电价的比例
- **取值范围**: 0 < sharp_peak_ratio ≤ 2
- **影响**: 影响电价计算

#### `valley_ratio` (低谷比例)
- **参数名称**: 低谷比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.5815
- **说明**: 低谷电价相对于基准电价的比例
- **取值范围**: 0 < valley_ratio ≤ 2
- **影响**: 影响电价计算

## 配置示例

### 基本配置
```python
config_params = {
    # 核心系统参数
    'system_capacity_kw': 200,          # 系统容量(kW)
    'system_energy_kwh': 400,           # 系统电量(kWh) = 系统容量*2

    # 电网参数
    'transformer_capacity_kva': 1000,   # 变压器容量(kVA)
    'power_factor': 0.92,               # 功率因素
    'basic_fee_type': '实际',           # 基本电费类型

    # 运行参数
    'dod_percentage': 0.95,             # DOD百分比
    'daily_decay_rate': 0.000082,       # 日衰减率
    'peak1_discharge_rate': 0.5,        # 峰1放电倍率

    # 电价参数（新增）
    'sharp_peak_price': 1.29,           # 尖峰电价(元/kWh)
    'peak_price': 1.07,                 # 高峰电价(元/kWh)
    'flat_price': 0.62,                 # 平段电价(元/kWh)
    'valley_price': 0.26,               # 低谷电价(元/kWh)
    'demand_price': 43.2,               # 需量电价(元/kW)
    'capacity_price': 30.0,             # 按容电价(元/kVA)
    'power_agency_price': 0.05,         # 电力代理价(元/kWh)
    'transmission_price': 0.15,         # 输配电价(元/kWh)
    'additional_price': 0.02,           # 附加费(元/kWh)
    'peak_ratio': 0.7196,               # 高峰比例
    'sharp_peak_ratio': 0.2,            # 尖峰比例
    'valley_ratio': 0.5815,             # 低谷比例
    'actual_demand_kw': 1000,           # 实际需量(kW)
}
```

### 高容量配置
```python
config_params = {
    'system_capacity_kw': 500,          # 更大的系统容量
    'system_energy_kwh': 1000,          # 对应的系统电量
    'transformer_capacity_kva': 2500,   # 更大的变压器容量
    'dod_percentage': 0.90,             # 更保守的DOD
    'peak1_discharge_rate': 0.8,        # 更高的放电倍率
}
```

### 保守配置
```python
config_params = {
    'system_capacity_kw': 150,          # 较小的系统容量
    'system_energy_kwh': 300,           # 对应的系统电量
    'dod_percentage': 0.80,             # 更保守的DOD
    'daily_decay_rate': 0.0001,         # 更高的衰减率
    'peak1_discharge_rate': 0.3,        # 更保守的放电倍率
}
```

## 参数影响分析

### 系统容量的影响
- **直接影响**: 处理后瞬时功率的上限
- **间接影响**: 所有可充电量、可消纳电量的计算
- **建议**: 根据实际储能设备规格设定

### DOD百分比的影响
- **计算公式**: 系统留存 = 充电容量 * (1 - DOD百分比)
- **影响范围**: 所有循环计算都会减去系统留存
- **建议**: 0.80-0.95之间，过高影响电池寿命，过低影响经济性

### 峰1放电倍率的影响
- **计算公式**: 峰1可消纳电量 = 处理后8:00-8:45功率/4/0.5*峰1放电倍率
- **影响**: 直接影响峰1时段的放电能力
- **建议**: 根据电网调度要求和设备能力设定

## 参数验证

### 必要性检查
1. **系统容量 > 0**: 必须为正数
2. **DOD百分比**: 0 < dod_percentage ≤ 1
3. **功率因素**: 0 < power_factor ≤ 1
4. **衰减率**: 0 ≤ daily_decay_rate < 1

### 合理性检查
1. **系统电量 = 系统容量 * 2**: 按照逻辑规范要求
2. **变压器容量 ≥ 系统容量**: 电网容量应大于储能容量
3. **DOD百分比**: 建议0.80-0.95之间
4. **峰1放电倍率**: 建议0.3-0.8之间

## 使用建议

1. **首次配置**: 使用默认值进行初步计算
2. **参数调优**: 根据计算结果调整关键参数
3. **场景分析**: 使用不同参数组合进行对比分析
4. **定期更新**: 根据实际运行数据更新参数

## 注意事项

1. **参数一致性**: 确保所有参数在逻辑上一致
2. **单位统一**: 注意功率(kW)和电量(kWh)的单位区别
3. **数据来源**: 优先使用数据中台的实际数据
4. **影响评估**: 修改参数前评估对整体计算的影响
