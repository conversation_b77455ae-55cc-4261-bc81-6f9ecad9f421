#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能容量负荷测算程序
Energy Storage Capacity Load Calculation Program

基于负荷原始清单数据，计算储能系统的各项指标
Based on load original list data, calculate various indicators of energy storage system
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')


class EnergyStorageCalculator:
    """储能容量负荷测算器"""

    def __init__(self,
                 file_path: str,
                 load_sheet: str = "负荷-原始清单",
                 config_sheet: str = "容量-配置表",
                 pricing_sheet: str = "基础参数-电价"):
        """
        初始化计算器

        Args:
            file_path: Excel文件路径
            load_sheet: 负荷数据表名
            config_sheet: 配置表名
            pricing_sheet: 电价表名
        """
        self.file_path = file_path
        self.load_sheet = load_sheet
        self.config_sheet = config_sheet
        self.pricing_sheet = pricing_sheet

        # 默认参数
        self.default_params = {
            'dod_percentage': 0.95,  # DOD百分比
            'daily_decay_rate': 0.000082,  # 日衰减率
            'annual_decay_rate': 0.03,  # 年衰减率
            'power_factor': 0.92,  # 功率因素
            'peak1_discharge_rate': 0.5,  # 峰1放电倍率
            'system_capacity_kw': 200,  # 系统容量(kW)
            'system_energy_kwh': 400,  # 系统电量(kWh)
            'initial_system_energy': 400,  # 初始系统能量
            'is_demand_based': False,  # 企业是否按需(True)或按容(False)
            'transformer_capacity': 1000,  # 变压器容量(kVA)
            'actual_demand': 1000,  # 实际需量(kW)
        }

        # 时间段定义 (24小时制，15分钟间隔)
        self.time_periods = {
            'valley1': ('00:00:00', '07:45:00'),  # 谷1: 00:00-07:45
            'peak1_morning': ('08:00:00', '08:45:00'),  # 峰1早: 08:00-08:45
            'peak1': ('09:00:00', '10:45:00'),  # 尖1: 09:00-10:45
            'valley2': ('11:00:00', '1  2:45:00'),  # 谷2: 11:00-12:45
            'peak2_special': ('13:00:00', '16:45:00'),  # 尖2特殊: 13:00-16:45
            'peak2_normal': ('15:00:00', '16:45:00'),  # 尖2正常: 15:00-16:45
            'high2': ('13:00:00', '21:45:00'),  # 峰2: 13:00-21:45
            'night': ('22:00:00', '23:45:00'),  # 夜间: 22:00-23:45
            'late_night': ('22:00:00', '23:59:59'),  # 22-24时段
            'early_morning': ('00:00:00', '07:59:59'),  # 0-8时段
        }

    def load_data(self) -> Tuple[pd.DataFrame, Dict, Dict]:
        """
        加载Excel数据

        Returns:
            load_data: 负荷数据
            config_data: 配置数据
            pricing_data: 电价数据
        """
        try:
            # 读取负荷数据
            load_df = pd.read_excel(self.file_path, sheet_name=self.load_sheet)

            # 读取配置数据
            config_df = pd.read_excel(self.file_path, sheet_name=self.config_sheet)

            # 读取电价数据
            pricing_df = pd.read_excel(self.file_path, sheet_name=self.pricing_sheet)

            # 解析配置数据
            config_data = self._parse_config_data(config_df)

            # 解析电价数据
            pricing_data = self._parse_pricing_data(pricing_df)

            return load_df, config_data, pricing_data

        except Exception as e:
            raise Exception(f"数据加载失败: {str(e)}")

    def _parse_config_data(self, config_df: pd.DataFrame) -> Dict:
        """解析配置数据"""
        config = self.default_params.copy()

        try:
            # 从配置表中提取关键参数
            # 这里需要根据实际的配置表结构来调整
            if not config_df.empty:
                # 尝试从配置表中读取参数
                for idx, row in config_df.iterrows():
                    if pd.notna(row.iloc[0]):
                        if 'DOD' in str(row.iloc[0]):
                            config['dod_percentage'] = 0.95
                        elif '日衰减率' in str(row.iloc[0]):
                            if pd.notna(row.iloc[1]):
                                config['daily_decay_rate'] = float(row.iloc[1])
                        elif '年衰减率' in str(row.iloc[0]):
                            if pd.notna(row.iloc[1]):
                                config['annual_decay_rate'] = float(row.iloc[1])
                        elif '系统容量' in str(row.iloc[0]):
                            if pd.notna(row.iloc[1]):
                                config['system_capacity_kw'] = float(row.iloc[1])
                        elif '系统电量' in str(row.iloc[0]):
                            if pd.notna(row.iloc[1]):
                                config['system_energy_kwh'] = float(row.iloc[1])
        except Exception as e:
            print(f"配置数据解析警告: {str(e)}, 使用默认参数")

        return config

    def _parse_pricing_data(self, pricing_df: pd.DataFrame) -> Dict:
        """解析电价数据"""
        pricing_data = {}
        try:
            # 跳过前5行，实际数据从第6行开始
            for idx, row in pricing_df.iloc[5:].iterrows():
                # 月份在第0列，分类在第1列，尖峰电价在第8列，高峰电价在第9列，平段电价在第10列，低谷电价在第11列
                if pd.notna(row[0]) and str(row[0]).isdigit():
                    month = int(row[0])
                    category = row[1] if pd.notna(row[1]) else "正常"
                    pricing_data[month] = {
                        'category': category,
                        'peak_price': float(row[8]) if pd.notna(row[8]) else 1.288462,
                        'high_price': float(row[9]) if pd.notna(row[9]) else 1.073718,
                        'normal_price': float(row[10]) if pd.notna(row[10]) else 0.6244,
                        'valley_price': float(row[11]) if pd.notna(row[11]) else 0.261311,
                    }
        except Exception as e:
            print(f"电价数据解析警告: {str(e)}")
            # 设置默认电价数据
            for month in range(1, 13):
                pricing_data[month] = {
                    'category': "特殊" if month in [1, 7, 8, 12] else "正常",
                    'peak_price': 1.288462,
                    'high_price': 1.073718,
                    'normal_price': 0.6244,
                    'valley_price': 0.261311,
                }
        return pricing_data

    def preprocess_load_data(self, load_df: pd.DataFrame) -> pd.DataFrame:
        """
        预处理负荷数据

        Args:
            load_df: 原始负荷数据

        Returns:
            处理后的负荷数据
        """
        # 复制数据
        df = load_df.copy()

        # 清理时间列
        if 'Unnamed: 2' in df.columns:
            df['time'] = df['Unnamed: 2'].astype(str).str.replace('\t', '').str.strip()
        else:
            raise ValueError("未找到时间列")

        # 确保功率列存在
        if '瞬时有功' not in df.columns:
            raise ValueError("未找到功率列 '瞬时有功'")

        # 清理功率数据
        df['power'] = pd.to_numeric(df['瞬时有功'], errors='coerce')

        # 处理日期列
        df['date'] = pd.to_datetime(df['日期'])

        # 创建完整的日期时间列
        df['datetime'] = pd.to_datetime(df['date'].dt.strftime('%Y-%m-%d') + ' ' + df['time'])

        # 按日期时间排序
        df = df.sort_values(['date', 'datetime'])

        # 重置索引
        df = df.reset_index(drop=True)

        return df

    def _time_in_period(self, time_str: str, period_start: str, period_end: str) -> bool:
        """
        判断时间是否在指定时间段内

        Args:
            time_str: 时间字符串 (HH:MM:SS)
            period_start: 时间段开始 (HH:MM:SS)
            period_end: 时间段结束 (HH:MM:SS)

        Returns:
            是否在时间段内
        """
        try:
            time = datetime.strptime(time_str, '%H:%M:%S').time()
            start = datetime.strptime(period_start, '%H:%M:%S').time()
            end = datetime.strptime(period_end, '%H:%M:%S').time()

            if start <= end:
                return start <= time <= end
            else:
                # 跨天的情况
                return time >= start or time <= end
        except:
            return False

    def calculate_daily_metrics(self, df: pd.DataFrame, config: Dict, pricing_data: Dict) -> pd.DataFrame:
        """
        计算每日指标

        Returns:
            包含计算结果的DataFrame
        """
        # 按日期分组
        daily_groups = df.groupby(df['date'].dt.date)
        results = []
        all_processed_power = []  # 新增：存储所有天的96点功率
        prev_day_system_energy = config['initial_system_energy']
        prev_day_22_24 = 0
        # 预先计算每月最大瞬时功率
        df['month'] = df['date'].dt.month
        monthly_max_power = df.groupby('month')['power'].max().to_dict()
        for date, day_data in daily_groups:
            if len(day_data) != 96:
                print(f"警告: {date} 的数据点数不是96个 (实际: {len(day_data)})")
                continue
            month = date.month
            pricing_info = pricing_data.get(month, pricing_data[1])
            is_special = pricing_info['category'] == '特殊'
            # 动态计算功率上限
            if config.get('is_demand_based', False):
                # 按需：max(月最大瞬时功率*功率因素, 实际需量*功率因素)
                power_limit = max(monthly_max_power.get(month, 0) * config['power_factor'], config['actual_demand'] * config['power_factor'])
            else:
                # 按容：变压器容量*功率因素
                power_limit = config['transformer_capacity'] * config['power_factor']
            daily_result, processed_power = self._calculate_single_day_metrics(
                day_data, date, config, pricing_info, is_special,
                prev_day_system_energy, prev_day_22_24, return_power=True,
                power_limit=power_limit
            )
            results.append(daily_result)
            all_processed_power.append(processed_power)
            prev_day_system_energy = daily_result['系统留存']
            prev_day_22_24 = daily_result['22-24']
        df_results = pd.DataFrame(results)
        # 生成96个时间点的列名
        time_labels = [(datetime.strptime('00:00:00', '%H:%M:%S') + timedelta(minutes=15*i)).strftime('%H:%M:%S') for i in range(96)]
        for i, label in enumerate(time_labels):
            df_results[label] = [powers[i] if len(powers) == 96 else None for powers in all_processed_power]
        return df_results

    def _calculate_single_day_metrics(self, day_data: pd.DataFrame, date, config: Dict,
                                    pricing_info: Dict, is_special: bool,
                                    prev_day_system_energy: float, prev_day_22_24: float,
                                    return_power: bool = False,
                                    power_limit: float = None) -> Dict:
        """
        计算单日指标

        Args:
            day_data: 单日数据
            date: 日期
            config: 配置参数
            pricing_info: 电价信息
            is_special: 是否特殊电价
            prev_day_system_energy: 前一天系统能量
            prev_day_22_24: 前一天22-24时段值

        Returns:
            单日计算结果
        """
        # 基础信息
        result = {
            '日期': date,
            '月份': date.month,
            '电价分类': pricing_info['category']
        }

        # 1. 功率上限 (kW) - 月度计划需量
        result['功率上限'] = power_limit if power_limit is not None else config['system_capacity_kw']

        # 2. 尖最小功率 - 9:00-10:45 和 15:00-16:45 时段的最小瞬时功率
        peak_periods_power = []
        for _, row in day_data.iterrows():
            time_str = row['time']
            if (self._time_in_period(time_str, '09:00:00', '10:45:00') or
                self._time_in_period(time_str, '15:00:00', '16:45:00')):
                peak_periods_power.append(row['power'])

        result['尖最小功率'] = min(peak_periods_power) if peak_periods_power else 0

        # 3. 充电容量 - 前一天系统能量 - 初始系统能量 × 日衰减率
        result['充电容量'] = prev_day_system_energy - config['initial_system_energy'] * config['daily_decay_rate']

        # 4. 系统留存 - 前一天充电容量 × (1 - 95% DOD)
        result['系统留存'] = result['充电容量'] * (1 - config['dod_percentage'])

        # 5. 处理后每日96点的瞬时功率
        processed_power = []
        for _, row in day_data.iterrows():
            time_str = row['time']
            original_power = row['power']

            # 特定时间段使用不同的计算逻辑
            if (self._time_in_period(time_str, '00:00:00', '07:45:00') or
                self._time_in_period(time_str, '11:00:00', '12:45:00') or
                self._time_in_period(time_str, '22:00:00', '23:45:00')):
                # min[max(功率上限 - 原始功率, 0), 系统容量]
                processed = min(max(result['功率上限'] - original_power, 0), config['system_capacity_kw'])
            else:
                # min[max(原始功率, 0), 系统容量]
                processed = min(max(original_power, 0), config['system_capacity_kw'])

            processed_power.append(processed)

        # 将处理后的功率添加到day_data副本中
        day_data_copy = day_data.copy()
        day_data_copy['processed_power'] = processed_power

        result = self._calculate_time_period_metrics(result, day_data_copy, config, is_special, prev_day_22_24)
        if return_power:
            return result, processed_power
        return result

    def _calculate_time_period_metrics(self, result: Dict, day_data: pd.DataFrame,
                                     config: Dict, is_special: bool, prev_day_22_24: float) -> Dict:
        """
        计算时间段相关指标

        Args:
            result: 已有的计算结果
            day_data: 包含处理后功率的单日数据
            config: 配置参数
            is_special: 是否特殊电价
            prev_day_22_24: 前一天22-24时段值

        Returns:
            完整的单日计算结果
        """

        # 6. 22-24时段 - 22:00-24:00时段瞬时功率之和 ÷ 4
        late_night_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '22:00:00', '23:59:59'):
                late_night_power.append(row['processed_power'])
        result['22-24'] = sum(late_night_power) / 4 if late_night_power else 0

        # 7. 0-8时段 - 00:00-08:00时段瞬时功率之和 ÷ 4
        early_morning_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '00:00:00', '07:59:59'):
                early_morning_power.append(row['processed_power'])
        result['0-8'] = sum(early_morning_power) / 4 if early_morning_power else 0

        # 8. 谷1可充电量 - min(0-8值 + 前一天22-24值, 日充电容量)
        result['谷1可充电量'] = min(result['0-8'] + prev_day_22_24, result['充电容量'])

        # 9. 尖1可消纳电量 - min(9:00-10:45处理后瞬时功率之和 ÷ 4, 日充电容量)
        peak1_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '09:00:00', '10:45:00'):
                peak1_power.append(row['processed_power'])
        result['尖1可消纳电量'] = min(sum(peak1_power) / 4 if peak1_power else 0, result['充电容量'])

        # 10. 峰1可消纳电量 - min(8:00-8:45处理后瞬时功率之和 ÷ 4 ÷ 0.5 × 峰1放电倍率, 日充电容量)
        peak1_morning_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '08:00:00', '08:45:00'):
                peak1_morning_power.append(row['processed_power'])

        peak1_morning_sum = sum(peak1_morning_power) / 4 if peak1_morning_power else 0
        result['峰1可消纳电量'] = min(
            peak1_morning_sum / 0.5 * config['peak1_discharge_rate'],
            result['充电容量']
        )

        # 11. 谷2可充电量 - min(11:00-12:45处理后瞬时功率之和 ÷ 4, 日充电容量)
        valley2_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '11:00:00', '12:45:00'):
                valley2_power.append(row['processed_power'])
        result['谷2可充电量'] = min(sum(valley2_power) / 4 if valley2_power else 0, result['充电容量'])

        # 12. 尖2可消纳电量
        if is_special:
            # 特殊电价: min(13:00-16:45处理后瞬时功率之和 ÷ 4, 日充电容量)
            peak2_power = []
            for _, row in day_data.iterrows():
                if self._time_in_period(row['time'], '13:00:00', '16:45:00'):
                    peak2_power.append(row['processed_power'])
        else:
            # 正常电价: min(15:00-16:45处理后瞬时功率之和 ÷ 4, 日充电容量)
            peak2_power = []
            for _, row in day_data.iterrows():
                if self._time_in_period(row['time'], '15:00:00', '16:45:00'):
                    peak2_power.append(row['processed_power'])

        result['尖2可消纳电量'] = min(sum(peak2_power) / 4 if peak2_power else 0, result['充电容量'])

        # 13. 峰2可消耗 - min(13:00-21:45每日之和 ÷ 4 - 日尖2可消纳电量, 日充电容量)
        high2_power = []
        for _, row in day_data.iterrows():
            if self._time_in_period(row['time'], '13:00:00', '21:45:00'):
                high2_power.append(row['processed_power'])

        high2_sum = sum(high2_power) / 4 if high2_power else 0
        result['峰2可消耗'] = min(high2_sum - result['尖2可消纳电量'], result['充电容量'])

        return result

    def calculate_comprehensive_metrics(self, daily_results: pd.DataFrame, config: Dict, pricing_data: Dict) -> pd.DataFrame:
        """
        计算综合指标

        Args:
            daily_results: 每日计算结果
            config: 配置参数
            pricing_data: 电价数据

        Returns:
            包含综合指标的DataFrame
        """
        # 复制数据
        results = daily_results.copy()

        # 计算收益相关指标
        for idx, row in results.iterrows():
            month = row['月份']
            pricing_info = pricing_data.get(month, pricing_data[1])

            # 计算各时段收益
            results.loc[idx, '谷1收益'] = row['谷1可充电量'] * pricing_info['valley_price']
            results.loc[idx, '尖1收益'] = row['尖1可消纳电量'] * pricing_info['peak_price']
            results.loc[idx, '峰1收益'] = row['峰1可消纳电量'] * pricing_info['high_price']
            results.loc[idx, '谷2收益'] = row['谷2可充电量'] * pricing_info['valley_price']
            results.loc[idx, '尖2收益'] = row['尖2可消纳电量'] * pricing_info['peak_price']
            results.loc[idx, '峰2收益'] = row['峰2可消耗'] * pricing_info['high_price']

            # 计算总收益
            results.loc[idx, '日总收益'] = (
                results.loc[idx, '谷1收益'] + results.loc[idx, '尖1收益'] +
                results.loc[idx, '峰1收益'] + results.loc[idx, '谷2收益'] +
                results.loc[idx, '尖2收益'] + results.loc[idx, '峰2收益']
            )

            # 计算总放电量
            results.loc[idx, '日总放电量'] = (
                row['尖1可消纳电量'] + row['峰1可消纳电量'] +
                row['尖2可消纳电量'] + row['峰2可消耗']
            )

            # 计算总充电量
            results.loc[idx, '日总充电量'] = row['谷1可充电量'] + row['谷2可充电量']

        return results

    def run_calculation(self, custom_params: Optional[Dict] = None) -> pd.DataFrame:
        """
        运行完整的计算流程

        Args:
            custom_params: 自定义参数，会覆盖默认参数

        Returns:
            完整的计算结果DataFrame
        """
        print("开始加载数据...")
        load_df, config_data, pricing_data = self.load_data()

        # 合并自定义参数
        if custom_params:
            config_data.update(custom_params)

        print("开始预处理数据...")
        processed_df = self.preprocess_load_data(load_df)

        print("开始计算每日指标...")
        daily_results = self.calculate_daily_metrics(processed_df, config_data, pricing_data)

        print("开始计算综合指标...")
        final_results = self.calculate_comprehensive_metrics(daily_results, config_data, pricing_data)

        print("计算完成!")
        return final_results

    def export_results(self, results: pd.DataFrame, output_path: str, sheet_name: str = "计算结果"):
        """
        导出计算结果到Excel

        Args:
            results: 计算结果DataFrame
            output_path: 输出文件路径
            sheet_name: 工作表名称
        """
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                results.to_excel(writer, sheet_name=sheet_name, index=False)
            print(f"结果已导出到: {output_path}")
        except Exception as e:
            print(f"导出失败: {str(e)}")

    def get_summary_statistics(self, results: pd.DataFrame) -> Dict:
        """
        获取汇总统计信息

        Args:
            results: 计算结果DataFrame

        Returns:
            汇总统计信息
        """
        summary = {
            '总天数': len(results),
            '总收益': results['日总收益'].sum(),
            '平均日收益': results['日总收益'].mean(),
            '总放电量': results['日总放电量'].sum(),
            '平均日放电量': results['日总放电量'].mean(),
            '总充电量': results['日总充电量'].sum(),
            '平均日充电量': results['日总充电量'].mean(),
            '最大日收益': results['日总收益'].max(),
            '最小日收益': results['日总收益'].min(),
        }

        # 按月统计
        monthly_stats = results.groupby('月份').agg({
            '日总收益': ['sum', 'mean'],
            '日总放电量': ['sum', 'mean'],
            '日总充电量': ['sum', 'mean']
        }).round(2)

        summary['月度统计'] = monthly_stats

        return summary


def main():
    """主函数 - 示例用法"""

    # 文件路径
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"

    # 创建计算器实例
    calculator = EnergyStorageCalculator(file_path)

    # 自定义参数 (可选)
    custom_params = {
        'system_capacity_kw': 200,
        'system_energy_kwh': 400,
        'dod_percentage': 0.95,
        'daily_decay_rate': 0.000082,
        'peak1_discharge_rate': 0.5,
    }

    try:
        # 运行计算
        results = calculator.run_calculation(custom_params)

        # 显示前几行结果
        print("\n计算结果预览:")
        print(results.head())

        # 显示汇总统计
        print("\n汇总统计:")
        summary = calculator.get_summary_statistics(results)
        for key, value in summary.items():
            if key != '月度统计':
                print(f"{key}: {value}")

        # 导出结果
        output_path = r"f:\华云项目\ExcelToPython\储能容量负荷测算结果.xlsx"
        calculator.export_results(results, output_path)

        return results

    except Exception as e:
        print(f"计算过程中出现错误: {str(e)}")
        return None


if __name__ == "__main__":
    results = main()