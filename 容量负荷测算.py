#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容量负荷测算模块
Capacity and Load Calculation Module

本模块负责实现复杂的31步计算逻辑，包括：
- 功率上限计算
- 尖最小功率计算
- 充电容量和系统留存计算
- 处理后每日96点瞬时功率计算
- 各时段可充电量和可消纳电量计算
- 循环计算和DOD统计
- 日收益计算
"""

import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import warnings
from datetime import datetime, time


class CapacityLoadCalculator:
    """容量负荷计算器"""

    def __init__(self):
        """初始化计算器"""
        self.time_periods = {
            '谷1': ['00:00:00', '07:45:00'],  # 谷1时段
            '峰1': ['08:00:00', '08:45:00'],  # 峰1时段
            '尖1': ['09:00:00', '10:45:00'],  # 尖1时段
            '谷2': ['11:00:00', '12:45:00'],  # 谷2时段
            '尖2_特殊': ['13:00:00', '16:45:00'],  # 尖2时段（特殊电价）
            '尖2_正常': ['15:00:00', '16:45:00'],  # 尖2时段（正常电价）
            '峰2': ['13:00:00', '21:45:00'],  # 峰2时段
            '谷晚': ['22:00:00', '23:45:00'],  # 晚谷时段
        }

    def calculate_daily_metrics(self, load_data: pd.DataFrame,
                              capacity_results: Dict[str, Any],
                              fee_results: Dict[str, Any],
                              config_params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        计算每日容量负荷指标

        Args:
            load_data: 负荷数据
            capacity_results: 装机容量计算结果
            fee_results: 基本电费计算结果
            config_params: 配置参数

        Returns:
            pd.DataFrame: 每日计算结果
        """
        # 获取系统参数
        system_capacity = capacity_results.get('系统容量_kw', 200)
        system_energy = capacity_results.get('系统电量_kwh', 400)
        dod_percentage = capacity_results.get('dod_percentage', 0.95)
        daily_decay_rate = capacity_results.get('daily_decay_rate', 0.000082)
        peak1_discharge_rate = capacity_results.get('峰1放电倍率', 0.5)

        # 获取电价信息
        price_info = capacity_results.get('电价信息', {})
        monthly_prices = price_info.get('月度电价', {})

        # 按日期分组处理
        daily_results = []

        # 获取所有日期
        dates = load_data['日期'].dt.date.unique()
        dates = sorted(dates)

        # 初始化前一日数据
        prev_day_data = {
            '系统电量': system_energy,
            '循环2剩余': 0,
            '22-24': 0
        }

        for i, date in enumerate(dates):
            day_data = load_data[load_data['日期'].dt.date == date].copy()

            if day_data.empty:
                continue

            # 计算当日指标
            daily_result = self._calculate_single_day_metrics(
                date, day_data, system_capacity, system_energy, dod_percentage,
                daily_decay_rate, peak1_discharge_rate, monthly_prices,
                prev_day_data, is_first_day=(i == 0)
            )

            daily_results.append(daily_result)

            # 更新前一日数据
            prev_day_data = {
                '系统电量': daily_result.get('充电容量', system_energy),
                '循环2剩余': daily_result.get('循环2剩余', 0),
                '22-24': daily_result.get('22-24', 0)
            }

        # 转换为DataFrame
        results_df = pd.DataFrame(daily_results)

        # 确保96个时间点列按正确顺序排列
        time_columns = []
        for hour in range(24):
            for minute in [0, 15, 30, 45]:
                time_str = f"{hour:02d}:{minute:02d}:00"
                time_columns.append(time_str)

        # 重新排列列顺序：基本信息列 + 96个时间点列 + 计算结果列
        basic_columns = ['日期', '月份']
        calculation_columns = [
            '功率上限_kw', '尖最小功率', '充电容量', '系统留存',
            '22-24', '0-8', '谷1可充电量', '尖1可消纳电量', '峰1可消纳电量',
            '谷2可充电量', '尖2可消纳电量', '峰2可消耗', '谷1充电量',
            '峰1', '尖1', '循环1剩余', '谷2充电量', '尖2', '峰2', '循环2剩余',
            '循环1', '循环2', '折算天数', '循环1计数', '循环2计数', '两充两放',
            '尖放电', '总放电', '总收益', '满容量天数'
        ]

        # 确保所有列都存在
        all_columns = basic_columns + time_columns + calculation_columns
        existing_columns = [col for col in all_columns if col in results_df.columns]

        # 重新排序DataFrame
        results_df = results_df[existing_columns]

        return results_df

    def _calculate_single_day_metrics(self, date, day_data: pd.DataFrame,
                                    system_capacity: float, system_energy: float,
                                    dod_percentage: float, daily_decay_rate: float,
                                    peak1_discharge_rate: float, monthly_prices: Dict,
                                    prev_day_data: Dict, is_first_day: bool) -> Dict[str, Any]:
        """
        计算单日指标（31步计算逻辑）

        Args:
            date: 日期
            day_data: 当日数据
            system_capacity: 系统容量
            system_energy: 系统电量
            dod_percentage: DOD百分比
            daily_decay_rate: 日衰减率
            peak1_discharge_rate: 峰1放电倍率
            monthly_prices: 月度电价
            prev_day_data: 前一日数据
            is_first_day: 是否首日

        Returns:
            Dict[str, Any]: 单日计算结果
        """
        result = {
            '日期': date,
            '月份': date.month if hasattr(date, 'month') else pd.to_datetime(date).month
        }

        # 获取月度电价信息
        month = result['月份']
        month_price_info = monthly_prices.get(month, {})
        price_category = month_price_info.get('分类', '正常')

        # 1. 功率上限（每个月的功率上限就是对应月份的拟定需量）
        # 这个值应该从基本电费设定模块获取，这里使用默认值
        result['功率上限_kw'] = system_capacity  # 简化处理

        # 2. 尖最小功率（取每日9:00-10:45和15:00-16:45两个时间段内的瞬时功率的最小值）
        peak_min_power = self._calculate_peak_min_power(day_data)
        result['尖最小功率'] = peak_min_power

        # 3. 充电容量（每日充电容量=前一日系统电量-初始系统电量*日衰减率）
        if is_first_day:
            charging_capacity = system_energy
        else:
            charging_capacity = prev_day_data['系统电量'] - system_energy * daily_decay_rate
        result['充电容量'] = max(charging_capacity, 0)

        # 4. 系统留存（每日系统留存=前一日充电容量*（1-95%（DOD））
        system_retention = result['充电容量'] * (1 - dod_percentage)
        result['系统留存'] = system_retention

        # 5. 处理后每日96点的瞬时功率
        processed_power_96 = self._process_hourly_power(day_data, result['功率上限_kw'], system_capacity)

        # 将96点数据添加到结果中
        for time_point, power_value in processed_power_96.items():
            result[time_point] = power_value

        # 6. 22-24: 每日22-24=每日谷22：00-24:00时间段内所有瞬时功率相加除以4
        result['22-24'] = self._calculate_time_period_sum_div4(processed_power_96, '22:00:00', '23:45:00')

        # 7. 0-8: 每日0-8=每日谷00：00-08:00时间段内所有瞬时功率相加除以4
        result['0-8'] = self._calculate_time_period_sum_div4(processed_power_96, '00:00:00', '07:45:00')

        # 8. 谷1可充电量=min（每日0-8的值+前一日22-24的值，每日充电容量）
        valley1_chargeable = min(result['0-8'] + prev_day_data.get('22-24', 0), result['充电容量'])
        result['谷1可充电量'] = valley1_chargeable

        # 9. 尖1可消纳电量=（每日处理后的9:00-10:45的瞬时功率相加除以4，每日充电容量）两个中的最小值
        sharp1_consumable = min(
            self._calculate_time_period_sum_div4(processed_power_96, '09:00:00', '10:45:00'),
            result['充电容量']
        )
        result['尖1可消纳电量'] = sharp1_consumable

        # 10. 峰1可消纳电量=（每日处理后的8:00-8:45的瞬时功率相/4/0.5*峰1放电倍率,每日充电容量）两个中的最小值
        peak1_power_sum = self._calculate_time_period_sum_div4(processed_power_96, '08:00:00', '08:45:00')
        peak1_consumable_power = min(
            peak1_power_sum / 0.5 * peak1_discharge_rate,
            result['充电容量']
        )
        result['峰1可消纳电量'] = peak1_consumable_power

        # 11. 谷2可充电量=（每日处理后的11:00-12:45的瞬时功率相加/4,每日充电容量）中的最小值
        valley2_chargeable = min(
            self._calculate_time_period_sum_div4(processed_power_96, '11:00:00', '12:45:00'),
            result['充电容量']
        )
        result['谷2可充电量'] = valley2_chargeable

        # 12. 尖2可消纳电量（根据电价分类决定时间段）
        if price_category == '特殊':
            # 特殊电价：13:00-16:45
            sharp2_consumable = min(
                self._calculate_time_period_sum_div4(processed_power_96, '13:00:00', '16:45:00'),
                result['充电容量']
            )
        else:
            # 正常电价：15:00-16:45
            sharp2_consumable = min(
                self._calculate_time_period_sum_div4(processed_power_96, '15:00:00', '16:45:00'),
                result['充电容量']
            )
        result['尖2可消纳电量'] = sharp2_consumable

        # 13. 峰2可消耗=取（每日13:00-21:45的加总除4的结果值-每日尖2可消纳电量，每日充电容量）两个当中的最小值
        peak2_total = self._calculate_time_period_sum_div4(processed_power_96, '13:00:00', '21:45:00')
        peak2_consumable_power = min(peak2_total - result['尖2可消纳电量'], result['充电容量'])
        result['峰2可消耗'] = max(peak2_consumable_power, 0)

        # 继续计算循环相关指标
        result = self._calculate_cycle_metrics(result, prev_day_data, is_first_day)

        # 计算收益
        result = self._calculate_daily_revenue(result, month_price_info)

        return result

    def _calculate_cycle_metrics(self, result: Dict[str, Any],
                               prev_day_data: Dict, is_first_day: bool) -> Dict[str, Any]:
        """
        计算循环相关指标（步骤14-31）
        严格按照逻辑规范执行
        """
        # 14. 谷1充电量
        # 每日谷1充电量=min（每日谷1可充电量，每日充电容量-前一日的循环2剩余）
        # 首日谷1充电量=min(首日谷1可充电量,首日充电容量)
        if is_first_day:
            valley1_charge = min(result['谷1可充电量'], result['充电容量'])
        else:
            valley1_charge = min(
                result['谷1可充电量'],
                result['充电容量'] - prev_day_data.get('循环2剩余', 0)
            )
        result['谷1充电量'] = valley1_charge

        # 15. 峰1
        # 每日峰1=min（每日谷1充电量+前一日的循环2剩余-系统留存，峰1可消纳电量）
        # 首日峰1=min（每日谷1充电量-系统留存，峰1可消纳电量）
        if is_first_day:
            peak1 = min(
                result['谷1充电量'] - result['系统留存'],
                result['峰1可消纳电量']
            )
        else:
            peak1 = min(
                result['谷1充电量'] + prev_day_data.get('循环2剩余', 0) - result['系统留存'],
                result['峰1可消纳电量']
            )
        result['峰1'] = max(peak1, 0)

        # 16. 尖1
        # 每日尖1=min（每日谷1充电量+前一日循环2剩余-每日峰1-每日系统留存，每日尖1可消纳电量）
        # 首日尖1=min（每日谷1充电量-每日峰1-每日系统留存，每日尖1可消纳电量）
        if is_first_day:
            sharp1 = min(
                result['谷1充电量'] - result['峰1'] - result['系统留存'],
                result['尖1可消纳电量']
            )
        else:
            sharp1 = min(
                result['谷1充电量'] + prev_day_data.get('循环2剩余', 0) - result['峰1'] - result['系统留存'],
                result['尖1可消纳电量']
            )
        result['尖1'] = max(sharp1, 0)

        # 17. 循环1剩余
        # 每日循环1剩余=每日谷1充电量+前一日循环2剩余-每日峰1-每日尖1
        # 首日循环1剩余=每日谷1充电量-每日峰1-每日尖1
        if is_first_day:
            cycle1_remaining = result['谷1充电量'] - result['峰1'] - result['尖1']
        else:
            cycle1_remaining = (result['谷1充电量'] + prev_day_data.get('循环2剩余', 0)
                              - result['峰1'] - result['尖1'])
        result['循环1剩余'] = cycle1_remaining

        # 18. 谷2充电量
        # 每日谷2充电量=min（谷2可充电量，每日充电容量-循环1剩余）
        valley2_charge = min(result['谷2可充电量'], result['充电容量'] - result['循环1剩余'])
        result['谷2充电量'] = max(valley2_charge, 0)

        # 19. 尖2
        # 每日尖2=min（每日循环1剩余+每日谷2充电量-每日系统留存，尖2可消纳电量）
        sharp2 = min(
            result['循环1剩余'] + result['谷2充电量'] - result['系统留存'],
            result['尖2可消纳电量']
        )
        result['尖2'] = max(sharp2, 0)

        # 20. 峰2
        # 每日峰2=min（每日循环1剩余+每日谷2充电量-每日尖2-每日系统留存，峰2可消耗）
        peak2 = min(
            result['循环1剩余'] + result['谷2充电量'] - result['尖2'] - result['系统留存'],
            result['峰2可消耗']
        )
        result['峰2'] = max(peak2, 0)

        # 21. 循环2剩余
        # 每日循环2剩余=每日循环1剩余+每日谷2充电量-每日尖2-每日峰2
        cycle2_remaining = (result['循环1剩余'] + result['谷2充电量']
                          - result['尖2'] - result['峰2'])
        result['循环2剩余'] = cycle2_remaining

        # 22. 循环1
        # 循环1=峰1+尖1
        result['循环1'] = result['峰1'] + result['尖1']

        # 23. 循环2
        # 循环2=峰2+尖2
        result['循环2'] = result['峰2'] + result['尖2']

        # 24. 折算天数
        # 折算天数=（循环1+循环2）/充电容量/2
        if result['充电容量'] > 0:
            equivalent_days = (result['循环1'] + result['循环2']) / result['充电容量'] / 2
        else:
            equivalent_days = 0
        result['折算天数'] = equivalent_days

        # 25. 循环1计数
        # 每日循环1计数=循环1/充电容量
        if result['充电容量'] > 0:
            cycle1_count = result['循环1'] / result['充电容量']
        else:
            cycle1_count = 0
        result['循环1计数'] = cycle1_count

        # 26. 循环2计数
        # 每日循环2计数=循环2/充电容量
        if result['充电容量'] > 0:
            cycle2_count = result['循环2'] / result['充电容量']
        else:
            cycle2_count = 0
        result['循环2计数'] = cycle2_count

        # 27. 两充两放
        # 每日两充两放=如果当日循环1计数*循环2计数等于1，就是1，否则0
        if abs(result['循环1计数'] * result['循环2计数'] - 1) < 0.001:
            two_charge_discharge = 1
        else:
            two_charge_discharge = 0
        result['两充两放'] = two_charge_discharge

        # 28. 尖放电
        # 每日尖放电=每日尖1+每日尖2
        result['尖放电'] = result['尖1'] + result['尖2']

        # 29. 总放电
        # 每日总放电=每日循环1+每日循环2
        result['总放电'] = result['循环1'] + result['循环2']

        # 31. 满容量天数
        # 每日满容量天数=每日总放电/2/初始充电容量
        if result['充电容量'] > 0:
            full_capacity_days = result['总放电'] / 2 / result['充电容量']
        else:
            full_capacity_days = 0
        result['满容量天数'] = full_capacity_days

        return result

    def _calculate_daily_revenue(self, result: Dict[str, Any],
                               month_price_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算日收益（步骤30）
        严格按照逻辑规范：
        每日总收益=[每日尖放电*对应月度的尖峰电价*0.93+（每日总放电-每日尖放电）*对应月度的高峰电价*0.93-每日总放电*对应月份低谷电价/0.93]/10000
        """
        # 获取电价
        sharp_price = month_price_info.get('尖峰电价', 1.29)
        peak_price = month_price_info.get('高峰电价', 1.07)
        valley_price = month_price_info.get('低谷电价', 0.26)

        # 按照逻辑规范计算收益
        sharp_revenue = result['尖放电'] * sharp_price * 0.93
        other_discharge = result['总放电'] - result['尖放电']
        other_revenue = other_discharge * peak_price * 0.93
        valley_cost = result['总放电'] * valley_price / 0.93

        daily_revenue = (sharp_revenue + other_revenue - valley_cost) / 10000
        result['总收益'] = daily_revenue

        return result

    def _calculate_peak_min_power(self, day_data: pd.DataFrame) -> float:
        """
        计算尖最小功率
        """
        # 提取9:00-10:45和15:00-16:45时段的数据
        peak_times = ['09:00:00', '09:15:00', '09:30:00', '09:45:00', '10:00:00',
                     '10:15:00', '10:30:00', '10:45:00', '15:00:00', '15:15:00',
                     '15:30:00', '15:45:00', '16:00:00', '16:15:00', '16:30:00', '16:45:00']

        peak_powers = []
        for time_str in peak_times:
            time_data = day_data[day_data['时间'].str.contains(time_str, na=False)]
            if not time_data.empty:
                peak_powers.extend(time_data['瞬时有功'].tolist())

        return min(peak_powers) if peak_powers else 0

    def _process_hourly_power(self, day_data: pd.DataFrame,
                            power_limit: float, system_capacity: float) -> Dict[str, float]:
        """
        处理后每日96点的瞬时功率
        修正后的逻辑：
        1. 对于所有96个时间点，处理后的瞬时功率值应该直接等于原始数据表中的功率值
        2. 只有当原始功率值出现负数时，才需要进行修正处理
        3. 负数修正规则：
           - 对于00:00:00至07:45:00、11:00:00至12:45:00、22:00:00至23:45:00三个时间段：
             如果原始功率值为负数，则处理后功率值=min[max(功率上限-|原有功功率值|,0),系统容量]
           - 对于其余时间点：如果原始功率值为负数，则处理后功率值=0

        Returns:
            Dict[str, float]: 96个时间点的处理后功率值，键为时间字符串(HH:MM:SS)
        """
        # 生成完整的96个时间点
        time_points = []
        for hour in range(24):
            for minute in [0, 15, 30, 45]:
                time_str = f"{hour:02d}:{minute:02d}:00"
                time_points.append(time_str)

        # 创建时间到功率的映射
        power_map = {}
        for _, row in day_data.iterrows():
            time_str = str(row['时间']).replace('\t', '').strip()
            if ':' in time_str and len(time_str) >= 8:
                # 标准化时间格式
                time_parts = time_str.split(':')
                if len(time_parts) >= 2:
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    normalized_time = f"{hour:02d}:{minute:02d}:00"
                    power_map[normalized_time] = float(row['瞬时有功'])

        # 定义特殊处理时段（仅用于负数修正）
        special_periods = [
            ('00:00:00', '07:45:00'),
            ('11:00:00', '12:45:00'),
            ('22:00:00', '23:45:00')
        ]

        processed_power_96 = {}

        for time_point in time_points:
            original_power = power_map.get(time_point, 0.0)

            # 修正后的逻辑：只有负数才需要特殊处理
            if original_power >= 0:
                # 正数或零：直接使用原始值
                processed_power = original_power
            else:
                # 负数：需要修正处理
                in_special_period = self._is_in_special_periods(time_point, special_periods)

                if in_special_period:
                    # 特殊时段的负数修正：min[max(功率上限-|原有功功率值|,0),系统容量]
                    abs_original_power = abs(original_power)
                    processed_power = min(max(power_limit - abs_original_power, 0), system_capacity)
                else:
                    # 其余时间点的负数修正：设为0
                    processed_power = 0.0

            processed_power_96[time_point] = processed_power

        return processed_power_96

    def _is_in_special_periods(self, time_str: str, special_periods: list) -> bool:
        """
        检查时间点是否在特殊处理时段内
        """
        time_parts = time_str.split(':')
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        time_minutes = hour * 60 + minute

        for start_time, end_time in special_periods:
            start_parts = start_time.split(':')
            start_minutes = int(start_parts[0]) * 60 + int(start_parts[1])

            end_parts = end_time.split(':')
            end_minutes = int(end_parts[0]) * 60 + int(end_parts[1])

            if start_minutes <= time_minutes <= end_minutes:
                return True

        return False

    def _calculate_time_period_sum_div4(self, processed_power_96: Dict[str, float],
                                      start_time: str, end_time: str) -> float:
        """
        计算指定时段的瞬时功率相加除以4
        严格按照逻辑规范执行
        """
        start_parts = start_time.split(':')
        start_minutes = int(start_parts[0]) * 60 + int(start_parts[1])

        end_parts = end_time.split(':')
        end_minutes = int(end_parts[0]) * 60 + int(end_parts[1])

        total_power = 0.0
        count = 0

        for time_point, power_value in processed_power_96.items():
            time_parts = time_point.split(':')
            time_minutes = int(time_parts[0]) * 60 + int(time_parts[1])

            if start_minutes <= time_minutes <= end_minutes:
                total_power += power_value
                count += 1

        # 按照逻辑规范：相加除以4
        return total_power / 4 if count > 0 else 0.0

    def _time_in_range(self, time_str: str, start_time: str, end_time: str) -> bool:
        """
        检查时间是否在指定范围内
        """
        try:
            # 简化的时间比较
            time_str = time_str.replace('\t', '').strip()
            return start_time <= time_str <= end_time
        except:
            return False

    def _calculate_time_period_average(self, data: pd.DataFrame,
                                     start_time: str, end_time: str) -> float:
        """
        计算指定时段的平均功率
        """
        period_data = []

        for _, row in data.iterrows():
            time_str = str(row['时间']).strip()
            if self._time_in_range(time_str, start_time, end_time):
                if '处理后瞬时功率' in row:
                    period_data.append(row['处理后瞬时功率'])
                else:
                    period_data.append(row['瞬时有功'])

        if period_data:
            return sum(period_data) / 4  # 除以4是按照逻辑要求
        return 0


def calculate_capacity_load_metrics(load_data: pd.DataFrame,
                                  capacity_results: Dict[str, Any],
                                  fee_results: Dict[str, Any],
                                  config_params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """
    计算容量负荷指标（主要接口函数）

    Args:
        load_data: 负荷数据
        capacity_results: 装机容量计算结果
        fee_results: 基本电费计算结果
        config_params: 配置参数

    Returns:
        pd.DataFrame: 容量负荷计算结果
    """
    calculator = CapacityLoadCalculator()
    return calculator.calculate_daily_metrics(load_data, capacity_results, fee_results, config_params)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from 储能装机容量测算 import load_excel_data, calculate_energy_storage_capacity
    from 基本电费设定 import calculate_basic_electricity_fees

    file_path = "data/(厂商机密)储能容量测算模型.xlsx"

    try:
        # 加载数据
        load_data, price_data = load_excel_data(file_path)

        # 计算装机容量
        capacity_results = calculate_energy_storage_capacity(load_data, price_data)

        # 计算基本电费
        fee_results = calculate_basic_electricity_fees(load_data, capacity_results)

        # 计算容量负荷指标
        capacity_load_results = calculate_capacity_load_metrics(
            load_data, capacity_results, fee_results
        )

        print("容量负荷测算结果:")
        print("=" * 40)
        print(f"计算天数: {len(capacity_load_results)}")
        if not capacity_load_results.empty:
            print(f"总收益: {capacity_load_results['总收益'].sum():.4f}")
            print(f"平均日收益: {capacity_load_results['总收益'].mean():.4f}")
            print(f"总放电量: {capacity_load_results['总放电'].sum():.2f}")

    except Exception as e:
        print(f"计算失败: {str(e)}")