from 储能系统集成计算 import main_calculation_pipeline

config_params = {
    # 核心系统参数
    'system_capacity_kw': 200,          # 系统容量(kW)
    'system_energy_kwh': 400,           # 系统电量(kWh) = 系统容量*2

    # 电网参数
    'transformer_capacity_kva': 1000,   # 变压器容量(kVA)
    'power_factor': 0.92,               # 功率因素
    'basic_fee_type': '实际',           # 基本电费类型

    # 运行参数
    'dod_percentage': 0.95,             # DOD百分比
    'daily_decay_rate': 0.000082,       # 日衰减率
    'peak1_discharge_rate': 0.5,        # 峰1放电倍率

    # 电价参数（新增）
    'sharp_peak_price': 1.29,           # 尖峰电价(元/kWh)
    'peak_price': 1.07,                 # 高峰电价(元/kWh)
    'flat_price': 0.62,                 # 平段电价(元/kWh)
    'valley_price': 0.26,               # 低谷电价(元/kWh)
    'demand_price': 43.2,               # 需量电价(元/kW)
    'capacity_price': 30.0,             # 按容电价(元/kVA)
    'power_agency_price': 0.05,         # 电力代理价(元/kWh)
    'transmission_price': 0.15,         # 输配电价(元/kWh)
    'additional_price': 0.02,           # 附加费(元/kWh)
    'peak_ratio': 0.7196,               # 高峰比例
    'sharp_peak_ratio': 0.2,            # 尖峰比例
    'valley_ratio': 0.5815,             # 低谷比例
    'actual_demand_kw': 1000,           # 实际需量(kW)
}
# 从Excel文件计算
results = main_calculation_pipeline(
    file_path="data/(厂商机密)储能容量测算模型.xlsx",
    output_file="计算结果.xlsx",
    config_params=config_params
)