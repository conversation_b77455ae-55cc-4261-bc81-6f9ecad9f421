#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能系统集成计算模块
Energy Storage System Integrated Calculation Module

本模块整合所有四个计算模块，提供完整的储能系统计算流程：
1. 储能装机容量测算
2. 基本电费设定
3. 容量负荷测算
4. 汇总指标

主要功能：
- 数据平台接口
- 完整计算流程
- 结果导出
- 三个输出表生成
"""

import pandas as pd
from typing import Dict, Any, Optional, Tuple
import warnings
from datetime import datetime
import os

# 导入各个计算模块
from 储能装机容量测算 import EnergyStorageCapacityCalculator, load_excel_data
from 基本电费设定 import BasicElectricityFeeCalculator
from 容量负荷测算 import CapacityLoadCalculator
from 汇总指标 import SummaryIndicatorsCalculator


class EnergyStorageSystemCalculator:
    """储能系统集成计算器"""
    
    def __init__(self):
        """初始化集成计算器"""
        self.capacity_calculator = EnergyStorageCapacityCalculator()
        self.fee_calculator = BasicElectricityFeeCalculator()
        self.load_calculator = CapacityLoadCalculator()
        self.summary_calculator = SummaryIndicatorsCalculator()
    
    def calculate_from_data_platform(self, 
                                   load_data: pd.DataFrame,
                                   price_data: pd.DataFrame,
                                   config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从数据平台数据进行完整计算
        
        Args:
            load_data: 负荷原始清单数据
            price_data: 基础参数电价数据
            config_params: 配置参数
            
        Returns:
            Dict[str, Any]: 完整计算结果
        """
        results = {}
        
        try:
            # 步骤1: 储能装机容量测算
            print("正在计算储能装机容量...")
            capacity_results = self.capacity_calculator.load_data_from_platform(
                load_data, price_data, config_params
            )
            results['装机容量结果'] = capacity_results

            # 获取处理后的负荷数据（包含月份列）
            processed_load_data = self.capacity_calculator._process_load_data(load_data)

            # 步骤2: 基本电费设定
            print("正在计算基本电费...")
            fee_results = self.fee_calculator.calculate_monthly_fees(
                processed_load_data, capacity_results, config_params
            )
            results['基本电费结果'] = fee_results

            # 步骤3: 容量负荷测算
            print("正在计算容量负荷指标...")
            daily_results = self.load_calculator.calculate_daily_metrics(
                processed_load_data, capacity_results, fee_results, config_params
            )
            results['每日计算结果'] = daily_results

            # 步骤4: 汇总指标
            print("正在计算汇总指标...")
            summary_results = self.summary_calculator.calculate_annual_summary(
                daily_results, capacity_results, config_params
            )
            results['汇总指标结果'] = summary_results
            
            # 生成三个输出表
            results['输出表'] = self._generate_output_tables(
                capacity_results, fee_results, daily_results, summary_results
            )
            
            print("计算完成!")
            return results
            
        except Exception as e:
            print(f"计算过程中出现错误: {str(e)}")
            raise
    
    def calculate_from_excel_file(self, 
                                file_path: str,
                                config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从Excel文件进行完整计算
        
        Args:
            file_path: Excel文件路径
            config_params: 配置参数
            
        Returns:
            Dict[str, Any]: 完整计算结果
        """
        # 加载Excel数据
        print(f"正在加载Excel文件: {file_path}")
        load_data, price_data = load_excel_data(file_path)
        
        # 调用数据平台计算方法
        return self.calculate_from_data_platform(load_data, price_data, config_params)
    
    def _generate_output_tables(self, 
                              capacity_results: Dict[str, Any],
                              fee_results: Dict[str, Any],
                              daily_results: pd.DataFrame,
                              summary_results: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """
        生成三个输出表
        
        Args:
            capacity_results: 装机容量结果
            fee_results: 基本电费结果
            daily_results: 每日计算结果
            summary_results: 汇总指标结果
            
        Returns:
            Dict[str, pd.DataFrame]: 三个输出表
        """
        output_tables = {}
        
        # 输出表1: 装机容量和基本电费汇总
        table1_data = []
        
        # 装机容量信息
        capacity_summary = self.capacity_calculator.get_capacity_summary(capacity_results)
        for key, value in capacity_summary.items():
            table1_data.append({'指标类别': '装机容量', '指标名称': key, '数值': value, '单位': self._get_unit(key)})
        
        # 基本电费信息
        fee_summary = self.fee_calculator.get_fee_summary(fee_results)
        for key, value in fee_summary.items():
            table1_data.append({'指标类别': '基本电费', '指标名称': key, '数值': value, '单位': self._get_unit(key)})
        
        output_tables['装机容量和基本电费汇总表'] = pd.DataFrame(table1_data)
        
        # 输出表2: 每日详细计算结果
        output_tables['每日详细计算结果表'] = daily_results.copy()
        
        # 输出表3: 年度汇总指标
        table3_data = []
        for key, value in summary_results.items():
            if key not in ['月度统计', '系统参数']:  # 排除复杂嵌套数据
                table3_data.append({'指标名称': key, '数值': value, '单位': self._get_unit(key)})
        
        output_tables['年度汇总指标表'] = pd.DataFrame(table3_data)
        
        return output_tables
    
    def _get_unit(self, indicator_name: str) -> str:
        """
        获取指标单位
        
        Args:
            indicator_name: 指标名称
            
        Returns:
            str: 单位
        """
        unit_mapping = {
            '变压器容量(kVA)': 'kVA',
            '功率上限(kW)': 'kW',
            '系统容量(kW)': 'kW',
            '系统电量(kWh)': 'kWh',
            '年度总用电量(万kWh)': '万kWh',
            '年度总需量缴费(元)': '元',
            '年度总按容缴费(元)': '元',
            '年度总增加费用(元)': '元',
            '平均拟定需量(kW)': 'kW',
            '全年电量_万kwh': '万kWh',
            '总利润': '元',
            '平均日收益': '元',
            '最大日收益': '元',
            '最小日收益': '元',
            '平均充电容量': 'kWh',
            '平均系统留存': 'kWh',
        }
        
        return unit_mapping.get(indicator_name, '')
    
    def export_results_to_excel(self, 
                              results: Dict[str, Any],
                              output_file_path: str) -> None:
        """
        导出结果到Excel文件
        
        Args:
            results: 计算结果
            output_file_path: 输出文件路径
        """
        try:
            with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                # 导出三个输出表
                output_tables = results.get('输出表', {})
                for table_name, table_data in output_tables.items():
                    table_data.to_excel(writer, sheet_name=table_name, index=False)
                
                # 导出汇总报告
                summary_results = results.get('汇总指标结果', {})
                if summary_results:
                    report = self.summary_calculator.get_summary_report(summary_results)
                    report_df = pd.DataFrame({'汇总报告': report.split('\n')})
                    report_df.to_excel(writer, sheet_name='汇总报告', index=False)
                
                print(f"结果已导出到: {output_file_path}")
                
        except Exception as e:
            print(f"导出Excel文件失败: {str(e)}")
            raise
    
    def get_calculation_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取计算摘要
        
        Args:
            results: 计算结果
            
        Returns:
            Dict[str, Any]: 计算摘要
        """
        summary = {}
        
        # 基本信息
        daily_results = results.get('每日计算结果', pd.DataFrame())
        summary_results = results.get('汇总指标结果', {})
        
        if not daily_results.empty:
            summary.update({
                '计算天数': len(daily_results),
                '总收益': summary_results.get('总利润', 0),
                '平均日收益': summary_results.get('平均日收益', 0),
                '全年电量': summary_results.get('全年电量_万kwh', 0),
                '尖放电占比': summary_results.get('尖放电占比', 0),
                '2次循环天数': summary_results.get('2次循环天数', 0),
                '折算天数汇总': summary_results.get('折算天数汇总', 0),
            })
        
        return summary


def main_calculation_pipeline(file_path: str, 
                            config_params: Optional[Dict[str, Any]] = None,
                            output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    主要计算流程（对外接口函数）
    
    Args:
        file_path: Excel文件路径
        config_params: 配置参数
        output_file: 输出文件路径
        
    Returns:
        Dict[str, Any]: 完整计算结果
    """
    calculator = EnergyStorageSystemCalculator()
    
    # 执行完整计算
    results = calculator.calculate_from_excel_file(file_path, config_params)
    
    # 导出结果
    if output_file:
        calculator.export_results_to_excel(results, output_file)
    
    # 显示摘要
    summary = calculator.get_calculation_summary(results)
    print("\n计算摘要:")
    print("=" * 40)
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    return results


if __name__ == "__main__":
    # 测试完整计算流程
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    output_file = "储能系统完整计算结果.xlsx"
    
    try:
        results = main_calculation_pipeline(file_path, output_file=output_file)
        print("\n完整计算流程测试成功!")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
