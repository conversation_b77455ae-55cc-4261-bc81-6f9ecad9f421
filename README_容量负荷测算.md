# 储能容量负荷测算程序使用说明

## 概述

本程序基于负荷原始清单数据，计算储能系统的各项指标，包括功率上限、充电容量、系统留存、各时段可充电量和可消纳电量等27个关键指标。

## 功能特点

- **数据处理**: 自动处理96个时间点/天的15分钟间隔数据
- **灵活配置**: 支持自定义系统参数和电价策略
- **多场景分析**: 支持不同参数组合的对比分析
- **结果导出**: 自动生成Excel格式的计算结果
- **错误处理**: 完善的数据验证和异常处理机制

## 安装要求

```bash
pip install pandas openpyxl
```

## 基本用法

### 1. 简单使用

```python
from 容量负荷测算 import EnergyStorageCalculator

# 创建计算器实例
calculator = EnergyStorageCalculator("数据文件路径.xlsx")

# 运行计算
results = calculator.run_calculation()

# 查看结果
print(results.head())
```

### 2. 自定义参数

```python
# 自定义参数
custom_params = {
    'system_capacity_kw': 300,      # 系统容量(kW)
    'system_energy_kwh': 600,       # 系统电量(kWh)
    'dod_percentage': 0.90,         # DOD百分比
    'daily_decay_rate': 0.000082,   # 日衰减率
    'peak1_discharge_rate': 0.6,    # 峰1放电倍率
}

# 使用自定义参数运行计算
results = calculator.run_calculation(custom_params)
```

### 3. 导出结果

```python
# 导出到Excel文件
calculator.export_results(results, "计算结果.xlsx", "结果表")

# 获取汇总统计
summary = calculator.get_summary_statistics(results)
print(summary)
```

## 计算指标说明

### 基础指标

1. **功率上限 (kW)**: 月度功率限制，等于对应月份的计划需量
2. **尖最小功率**: 9:00-10:45和15:00-16:45时段的最小瞬时功率
3. **充电容量**: 前一天系统能量 - 初始系统能量 × 日衰减率
4. **系统留存**: 前一天充电容量 × (1 - 95% DOD)

### 处理后功率计算

5. **处理后每日96点瞬时功率**:
   - 00:00-07:45, 11:00-12:45, 22:00-23:45: min[max(功率上限-原始功率, 0), 系统容量]
   - 其他时段: min[max(原始功率, 0), 系统容量]

### 时段指标

6. **22-24**: 22:00-24:00时段瞬时功率之和 ÷ 4
7. **0-8**: 00:00-08:00时段瞬时功率之和 ÷ 4
8. **谷1可充电量**: min(0-8值 + 前一天22-24值, 日充电容量)
9. **尖1可消纳电量**: min(9:00-10:45处理后瞬时功率之和 ÷ 4, 日充电容量)
10. **峰1可消纳电量**: min(8:00-8:45处理后瞬时功率之和 ÷ 4 ÷ 0.5 × 峰1放电倍率, 日充电容量)
11. **谷2可充电量**: min(11:00-12:45处理后瞬时功率之和 ÷ 4, 日充电容量)
12. **尖2可消纳电量**: 
    - 特殊电价: min(13:00-16:45处理后瞬时功率之和 ÷ 4, 日充电容量)
    - 正常电价: min(15:00-16:45处理后瞬时功率之和 ÷ 4, 日充电容量)
13. **峰2可消耗**: min(13:00-21:45每日之和 ÷ 4 - 日尖2可消纳电量, 日充电容量)

## 配置参数

### 系统参数

- `system_capacity_kw`: 系统容量(kW)，默认200
- `system_energy_kwh`: 系统电量(kWh)，默认400
- `initial_system_energy`: 初始系统能量，默认400

### 运行参数

- `dod_percentage`: DOD百分比，默认0.95
- `daily_decay_rate`: 日衰减率，默认0.000082
- `annual_decay_rate`: 年衰减率，默认0.03
- `power_factor`: 功率因素，默认0.92
- `peak1_discharge_rate`: 峰1放电倍率，默认0.5

## 数据格式要求

### Excel文件结构

1. **负荷-原始清单**: 包含96个时间点/天的负荷数据
   - 用户名称列
   - 日期列
   - 时间列 (HH:MM:SS格式)
   - 瞬时有功功率列

2. **容量-配置表**: 系统配置参数
3. **基础参数-电价**: 月度电价信息

### 时间格式

- 每天96个时间点，15分钟间隔
- 时间格式: HH:MM:SS (如 "09:15:00")
- 支持跨天时间段计算

## 输出结果

### 主要输出列

- 日期、月份、电价分类
- 功率上限、尖最小功率
- 充电容量、系统留存
- 各时段可充电量和可消纳电量
- 各时段收益
- 日总收益、日总放电量、日总充电量

### 汇总统计

- 总天数、总收益、平均日收益
- 总放电量、总充电量
- 最大/最小日收益
- 月度统计信息

## 示例代码

完整的使用示例请参考 `test_容量负荷测算.py` 文件，包含：

- 基本计算测试
- 自定义参数测试
- 月度分析测试
- 导出功能测试
- 多场景比较测试

## 注意事项

1. 确保Excel文件路径正确且文件可访问
2. 数据中如果某天的时间点数不是96个，程序会发出警告但继续处理
3. 电价分类会影响尖2可消纳电量的计算逻辑
4. 建议在运行前备份原始数据文件

## 错误处理

程序包含完善的错误处理机制：

- 数据加载失败时会显示详细错误信息
- 配置数据解析失败时会使用默认参数
- 电价数据缺失时会使用默认电价
- 时间格式错误时会跳过相关计算

## 技术支持

如有问题或建议，请检查：

1. 数据文件格式是否正确
2. 参数设置是否合理
3. 系统环境是否满足要求

程序设计为高度可配置和可扩展，可根据具体需求调整计算逻辑和参数设置。
