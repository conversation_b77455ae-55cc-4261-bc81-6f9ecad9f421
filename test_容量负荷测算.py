#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能容量负荷测算程序测试脚本
Test script for Energy Storage Capacity Load Calculation Program
"""

from 容量负荷测算 import EnergyStorageCalculator
import pandas as pd


def test_basic_calculation():
    """测试基本计算功能"""
    print("=== 基本计算测试 ===")
    
    # 文件路径
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"
    
    # 创建计算器实例
    calculator = EnergyStorageCalculator(file_path)
    
    # 运行计算
    results = calculator.run_calculation()
    
    # 显示结果摘要
    print(f"计算完成，共处理 {len(results)} 天的数据")
    print(f"总收益: {results['日总收益'].sum():.2f}")
    print(f"平均日收益: {results['日总收益'].mean():.2f}")
    
    return results


def test_custom_parameters():
    """测试自定义参数"""
    print("\n=== 自定义参数测试 ===")
    
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"
    calculator = EnergyStorageCalculator(file_path)
    
    # 自定义参数
    custom_params = {
        'system_capacity_kw': 300,  # 增加系统容量
        'system_energy_kwh': 600,   # 增加系统电量
        'dod_percentage': 0.90,     # 降低DOD
        'peak1_discharge_rate': 0.6, # 提高峰1放电倍率
    }
    
    print("使用自定义参数:")
    for key, value in custom_params.items():
        print(f"  {key}: {value}")
    
    # 运行计算
    results = calculator.run_calculation(custom_params)
    
    # 显示结果摘要
    print(f"计算完成，共处理 {len(results)} 天的数据")
    print(f"总收益: {results['日总收益'].sum():.2f}")
    print(f"平均日收益: {results['日总收益'].mean():.2f}")
    
    return results


def test_monthly_analysis():
    """测试月度分析"""
    print("\n=== 月度分析测试 ===")
    
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"
    calculator = EnergyStorageCalculator(file_path)
    
    # 运行计算
    results = calculator.run_calculation()
    
    # 月度统计
    monthly_stats = results.groupby('月份').agg({
        '日总收益': ['count', 'sum', 'mean'],
        '日总放电量': ['sum', 'mean'],
        '日总充电量': ['sum', 'mean']
    }).round(2)
    
    print("月度统计结果:")
    print(monthly_stats)
    
    return monthly_stats


def test_export_functionality():
    """测试导出功能"""
    print("\n=== 导出功能测试 ===")
    
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"
    calculator = EnergyStorageCalculator(file_path)
    
    # 运行计算
    results = calculator.run_calculation()
    
    # 导出到不同文件
    output_files = [
        ("基本计算结果.xlsx", "基本计算"),
        ("详细分析结果.xlsx", "详细分析"),
    ]
    
    for output_file, sheet_name in output_files:
        output_path = f"f:\\华云项目\\ExcelToPython\\{output_file}"
        calculator.export_results(results, output_path, sheet_name)
        print(f"已导出: {output_file}")


def compare_scenarios():
    """比较不同场景"""
    print("\n=== 场景比较测试 ===")
    
    file_path = r"f:\华云项目\ExcelToPython\data\(厂商机密)储能容量测算模型.xlsx"
    calculator = EnergyStorageCalculator(file_path)
    
    # 定义不同场景
    scenarios = {
        '基准场景': {},
        '高容量场景': {
            'system_capacity_kw': 300,
            'system_energy_kwh': 600,
        },
        '保守场景': {
            'dod_percentage': 0.80,
            'daily_decay_rate': 0.0001,
        },
        '激进场景': {
            'dod_percentage': 0.95,
            'peak1_discharge_rate': 0.8,
        }
    }
    
    comparison_results = {}
    
    for scenario_name, params in scenarios.items():
        print(f"\n计算 {scenario_name}...")
        results = calculator.run_calculation(params)
        
        comparison_results[scenario_name] = {
            '总收益': results['日总收益'].sum(),
            '平均日收益': results['日总收益'].mean(),
            '总放电量': results['日总放电量'].sum(),
            '总充电量': results['日总充电量'].sum(),
        }
    
    # 显示比较结果
    print("\n场景比较结果:")
    comparison_df = pd.DataFrame(comparison_results).T
    print(comparison_df.round(2))
    
    return comparison_df


def main():
    """主测试函数"""
    print("储能容量负荷测算程序测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_basic_calculation()
        test_custom_parameters()
        test_monthly_analysis()
        test_export_functionality()
        compare_scenarios()
        
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    main()
