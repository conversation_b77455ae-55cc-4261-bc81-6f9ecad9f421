#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本电费设定模块
Basic Electricity Fee Configuration Module

本模块负责计算基本电费相关参数，包括：
- 月份识别
- 拟定需量计算
- 用电量计算
- 需量缴费和按容缴费
- 负载率计算
- 增加费用计算
"""

import pandas as pd
from typing import Dict, Any, Optional
import warnings


class BasicElectricityFeeCalculator:
    """基本电费计算器"""

    def __init__(self):
        """初始化计算器"""
        self.default_params = {
            'transformer_capacity_kva': 2600,  # 变压器容量(kVA)
            'power_factor': 0.92,  # 功率因素
            'actual_demand_kw': 1000,  # 实际需量(kW)
            'demand_price': 43.2,  # 需量电价(元/kW)
            'capacity_price': 30.0,  # 按容电价(元/kVA)
        }

    def calculate_monthly_fees(self, load_data: pd.DataFrame,
                             capacity_results: Dict[str, Any],
                             config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        计算月度基本电费

        Args:
            load_data: 负荷数据
            capacity_results: 装机容量计算结果
            config_params: 配置参数

        Returns:
            Dict[str, Any]: 基本电费计算结果
        """
        # 合并参数
        params = self.default_params.copy()
        if config_params:
            params.update(config_params)

        # 从装机容量结果中获取参数
        transformer_capacity = capacity_results.get('变压器容量_kva', params['transformer_capacity_kva'])
        power_factor = capacity_results.get('功率因素', params['power_factor'])
        fee_type = capacity_results.get('基本电费类型', '实际')

        results = {}

        # 按月份计算
        monthly_results = {}

        if not load_data.empty:
            # 按月份分组计算
            for month in load_data['月份'].unique():
                month_data = load_data[load_data['月份'] == month]
                monthly_result = self._calculate_single_month_fee(
                    month, month_data, transformer_capacity, power_factor,
                    fee_type, params
                )
                monthly_results[month] = monthly_result

        results['月度结果'] = monthly_results
        results['年度汇总'] = self._calculate_annual_summary(monthly_results)

        return results

    def _calculate_single_month_fee(self, month: int, month_data: pd.DataFrame,
                                  transformer_capacity: float, power_factor: float,
                                  fee_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算单月基本电费

        Args:
            month: 月份
            month_data: 月度数据
            transformer_capacity: 变压器容量
            power_factor: 功率因素
            fee_type: 基本电费类型
            params: 参数字典

        Returns:
            Dict[str, Any]: 单月计算结果
        """
        result = {}

        # 1. 月份
        result['月份'] = month

        # 2. 拟定需量计算
        if fee_type == '按容':
            # 如果企业按容，则拟定需量=变压器容量*功率因素
            planned_demand = transformer_capacity * power_factor
        else:
            # 如果企业按需，则取最大值
            if not month_data.empty:
                max_power = month_data['瞬时有功'].max()
                actual_demand = params.get('actual_demand_kw', 1000)
                planned_demand = max(max_power * power_factor, actual_demand * power_factor)
            else:
                planned_demand = transformer_capacity * power_factor

        result['拟定需量_kw'] = planned_demand

        # 3. 用电量计算（企业对应月份内所有瞬时功率相加除以40000）
        if not month_data.empty:
            total_power = month_data['瞬时有功'].sum()
            electricity_consumption = total_power / 40000  # 万kWh
        else:
            electricity_consumption = 0

        result['用电量_万kwh'] = electricity_consumption

        # 4. 需量缴费
        demand_price = params.get('demand_price', 43.2)
        demand_fee = planned_demand * demand_price if fee_type in ['按需', '实际'] else 0
        result['需量缴费'] = demand_fee

        # 5. 按容缴费
        capacity_price = params.get('capacity_price', 30.0)
        capacity_fee = transformer_capacity * capacity_price if fee_type == '按容' else 0
        result['按容缴费'] = capacity_fee

        # 6. 负载率计算
        load_rate = planned_demand / transformer_capacity if transformer_capacity > 0 else 0
        result['负载率'] = load_rate

        # 7. 实际需量
        actual_demand = params.get('actual_demand_kw', 1000)
        result['实际需量_kw'] = actual_demand

        # 8. 增加费用（近一年按容缴费金额-近一年按需缴费金额）
        # 这里简化为单月计算，实际应该是年度计算
        annual_capacity_fee = transformer_capacity * capacity_price * 12
        annual_demand_fee = planned_demand * demand_price * 12
        additional_fee = annual_capacity_fee - annual_demand_fee
        result['增加费用'] = additional_fee

        return result

    def _calculate_annual_summary(self, monthly_results: Dict[int, Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算年度汇总

        Args:
            monthly_results: 月度结果字典

        Returns:
            Dict[str, Any]: 年度汇总结果
        """
        if not monthly_results:
            return {}

        summary = {}

        # 汇总各项指标
        total_electricity = sum(result.get('用电量_万kwh', 0) for result in monthly_results.values())
        total_demand_fee = sum(result.get('需量缴费', 0) for result in monthly_results.values())
        total_capacity_fee = sum(result.get('按容缴费', 0) for result in monthly_results.values())
        total_additional_fee = sum(result.get('增加费用', 0) for result in monthly_results.values())

        # 平均负载率
        load_rates = [result.get('负载率', 0) for result in monthly_results.values()]
        avg_load_rate = sum(load_rates) / len(load_rates) if load_rates else 0

        # 平均拟定需量
        planned_demands = [result.get('拟定需量_kw', 0) for result in monthly_results.values()]
        avg_planned_demand = sum(planned_demands) / len(planned_demands) if planned_demands else 0

        summary.update({
            '年度总用电量_万kwh': total_electricity,
            '年度总需量缴费': total_demand_fee,
            '年度总按容缴费': total_capacity_fee,
            '年度总增加费用': total_additional_fee,
            '平均负载率': avg_load_rate,
            '平均拟定需量_kw': avg_planned_demand,
            '月份数量': len(monthly_results),
        })

        return summary

    def get_fee_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取基本电费计算摘要

        Args:
            results: 计算结果

        Returns:
            Dict[str, Any]: 摘要信息
        """
        annual_summary = results.get('年度汇总', {})
        monthly_results = results.get('月度结果', {})

        summary = {
            '计算月份数': len(monthly_results),
            '年度总用电量(万kWh)': annual_summary.get('年度总用电量_万kwh', 0),
            '年度总需量缴费(元)': annual_summary.get('年度总需量缴费', 0),
            '年度总按容缴费(元)': annual_summary.get('年度总按容缴费', 0),
            '年度总增加费用(元)': annual_summary.get('年度总增加费用', 0),
            '平均负载率': annual_summary.get('平均负载率', 0),
            '平均拟定需量(kW)': annual_summary.get('平均拟定需量_kw', 0),
        }

        return summary


def calculate_basic_electricity_fees(load_data: pd.DataFrame,
                                   capacity_results: Dict[str, Any],
                                   config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    计算基本电费（主要接口函数）

    Args:
        load_data: 负荷数据
        capacity_results: 装机容量计算结果
        config_params: 配置参数

    Returns:
        Dict[str, Any]: 基本电费计算结果
    """
    calculator = BasicElectricityFeeCalculator()
    return calculator.calculate_monthly_fees(load_data, capacity_results, config_params)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from 储能装机容量测算 import load_excel_data, calculate_energy_storage_capacity

    file_path = "data/(厂商机密)储能容量测算模型.xlsx"

    try:
        # 加载数据
        load_data, price_data = load_excel_data(file_path)

        # 计算装机容量
        capacity_results = calculate_energy_storage_capacity(load_data, price_data)

        # 计算基本电费
        fee_results = calculate_basic_electricity_fees(load_data, capacity_results)

        # 显示摘要
        calculator = BasicElectricityFeeCalculator()
        summary = calculator.get_fee_summary(fee_results)

        print("基本电费计算结果:")
        print("=" * 40)
        for key, value in summary.items():
            print(f"{key}: {value}")

    except Exception as e:
        print(f"计算失败: {str(e)}")