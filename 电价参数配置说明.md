# 电价参数配置说明

## 概述

储能系统计算已从Excel表格读取电价信息改为通过配置参数传入。本文档详细说明新的电价参数配置方式。

## 修正说明

### 修正前（旧方式）
- 电价信息从Excel文件的"基础参数-电价"工作表中读取
- 依赖Excel文件格式，不够灵活
- 难以与数据平台集成

### 修正后（新方式）
- 电价信息通过`config_params`参数字典传入
- 完全独立于Excel文件，更加灵活
- 便于与数据平台集成

## 电价参数列表

### 基本电价参数

#### `sharp_peak_price` (尖峰电价)
- **参数名称**: 尖峰电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 1.29
- **说明**: 尖峰时段的电价
- **影响**: 影响尖放电收益计算

#### `peak_price` (高峰电价)
- **参数名称**: 高峰电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 1.07
- **说明**: 高峰时段的电价
- **影响**: 影响峰时段放电收益计算

#### `flat_price` (平段电价)
- **参数名称**: 平段电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.62
- **说明**: 平段时段的电价
- **影响**: 影响平时段放电收益计算

#### `valley_price` (低谷电价)
- **参数名称**: 低谷电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.26
- **说明**: 低谷时段的电价
- **影响**: 影响充电成本计算

### 基本电费参数

#### `demand_price` (需量电价)
- **参数名称**: 需量电价
- **数据类型**: float
- **单位**: 元/kW
- **默认值**: 43.2
- **说明**: 按需量计费的电价
- **影响**: 影响需量缴费计算

#### `capacity_price` (按容电价)
- **参数名称**: 按容电价
- **数据类型**: float
- **单位**: 元/kVA
- **默认值**: 30.0
- **说明**: 按容量计费的电价
- **影响**: 影响按容缴费计算

### 附加费用参数

#### `power_agency_price` (电力代理价)
- **参数名称**: 电力代理价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.05
- **说明**: 电力代理服务费用
- **影响**: 影响总电费计算

#### `transmission_price` (输配电价)
- **参数名称**: 输配电价
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.15
- **说明**: 输配电网费用
- **影响**: 影响总电费计算

#### `additional_price` (附加费)
- **参数名称**: 附加费
- **数据类型**: float
- **单位**: 元/kWh
- **默认值**: 0.02
- **说明**: 其他附加费用
- **影响**: 影响总电费计算

### 电价比例参数

#### `peak_ratio` (高峰比例)
- **参数名称**: 高峰比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.7196
- **说明**: 高峰电价相对于基准电价的比例
- **影响**: 影响电价计算

#### `sharp_peak_ratio` (尖峰比例)
- **参数名称**: 尖峰比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.2
- **说明**: 尖峰电价相对于基准电价的比例
- **影响**: 影响电价计算

#### `valley_ratio` (低谷比例)
- **参数名称**: 低谷比例
- **数据类型**: float
- **单位**: 无量纲
- **默认值**: 0.5815
- **说明**: 低谷电价相对于基准电价的比例
- **影响**: 影响电价计算

## 配置示例

### 基本配置
```python
config_params = {
    # 基本电价参数
    'sharp_peak_price': 1.29,  # 尖峰电价(元/kWh)
    'peak_price': 1.07,        # 高峰电价(元/kWh)
    'flat_price': 0.62,        # 平段电价(元/kWh)
    'valley_price': 0.26,      # 低谷电价(元/kWh)
    
    # 基本电费参数
    'demand_price': 43.2,      # 需量电价(元/kW)
    'capacity_price': 30.0,    # 按容电价(元/kVA)
    
    # 附加费用参数
    'power_agency_price': 0.05,    # 电力代理价(元/kWh)
    'transmission_price': 0.15,    # 输配电价(元/kWh)
    'additional_price': 0.02,      # 附加费(元/kWh)
    
    # 电价比例参数
    'peak_ratio': 0.7196,          # 高峰比例
    'sharp_peak_ratio': 0.2,       # 尖峰比例
    'valley_ratio': 0.5815,        # 低谷比例
}
```

### 夏季高电价配置
```python
summer_config = {
    'sharp_peak_price': 1.45,  # 夏季尖峰电价更高
    'peak_price': 1.20,        # 夏季高峰电价更高
    'flat_price': 0.65,        # 夏季平段电价略高
    'valley_price': 0.28,      # 夏季低谷电价略高
    'demand_price': 45.0,      # 夏季需量电价更高
    'capacity_price': 32.0,    # 夏季按容电价更高
}
```

### 工业用电配置
```python
industrial_config = {
    'sharp_peak_price': 1.35,  # 工业尖峰电价
    'peak_price': 1.15,        # 工业高峰电价
    'flat_price': 0.70,        # 工业平段电价
    'valley_price': 0.30,      # 工业低谷电价
    'demand_price': 50.0,      # 工业需量电价
    'capacity_price': 35.0,    # 工业按容电价
}
```

## 使用方法

### 方法1：直接传入参数
```python
from 储能系统集成计算 import main_calculation_pipeline

config_params = {
    'sharp_peak_price': 1.29,
    'peak_price': 1.07,
    'valley_price': 0.26,
    # ... 其他参数
}

results = main_calculation_pipeline(
    file_path="data/input.xlsx",
    config_params=config_params,
    output_file="results.xlsx"
)
```

### 方法2：使用数据平台接口
```python
from 储能系统集成计算 import EnergyStorageSystemCalculator

calculator = EnergyStorageSystemCalculator()

# 从数据平台获取电价信息
price_config = get_price_from_platform()  # 假设的数据平台接口

results = calculator.calculate_from_data_platform(
    load_data=load_data,
    price_data=None,  # 不再使用Excel电价数据
    config_params=price_config
)
```

### 方法3：混合配置
```python
# 基本配置
base_config = {
    'system_capacity_kw': 200,
    'system_energy_kwh': 400,
}

# 电价配置
price_config = {
    'sharp_peak_price': 1.29,
    'peak_price': 1.07,
    'valley_price': 0.26,
}

# 合并配置
config_params = {**base_config, **price_config}

results = main_calculation_pipeline(
    file_path="data/input.xlsx",
    config_params=config_params
)
```

## 兼容性说明

### 向后兼容
- 如果未提供电价参数，系统将使用默认值
- Excel文件中的"基础参数-电价"工作表不再被使用，但不会导致错误
- 现有的调用方式仍然有效

### 迁移建议
1. **逐步迁移**: 可以先使用默认电价参数，然后逐步添加自定义电价
2. **参数验证**: 建议在传入电价参数前进行合理性验证
3. **文档更新**: 更新相关文档和配置文件

## 注意事项

### 参数验证
```python
def validate_price_params(config_params):
    """验证电价参数的合理性"""
    price_params = [
        'sharp_peak_price', 'peak_price', 'flat_price', 'valley_price'
    ]
    
    for param in price_params:
        if param in config_params:
            value = config_params[param]
            if not isinstance(value, (int, float)) or value <= 0:
                raise ValueError(f"电价参数 {param} 必须为正数")
    
    # 验证电价关系：尖峰 > 高峰 > 平段 > 低谷
    if all(param in config_params for param in price_params):
        sharp = config_params['sharp_peak_price']
        peak = config_params['peak_price']
        flat = config_params['flat_price']
        valley = config_params['valley_price']
        
        if not (sharp >= peak >= flat >= valley):
            print("警告：电价关系可能不合理，建议检查")
```

### 单位一致性
- 确保所有电价单位为元/kWh
- 确保需量电价单位为元/kW
- 确保按容电价单位为元/kVA

### 数据来源
- 优先使用数据平台的实时电价数据
- 定期更新电价参数以反映最新政策
- 建立电价参数的版本管理机制

## 影响分析

### 对计算结果的影响
- **收益计算**: 电价直接影响储能系统的收益计算
- **成本分析**: 低谷电价影响充电成本
- **经济性评估**: 电价差异决定储能系统的经济性

### 对系统性能的影响
- **灵活性提升**: 不再依赖Excel文件格式
- **集成便利**: 更容易与数据平台集成
- **维护简化**: 电价更新不需要修改Excel文件

这种新的电价参数配置方式提供了更大的灵活性和更好的可维护性，同时保持了向后兼容性。
