#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汇总指标模块
Summary Indicators Module

本模块负责计算年度汇总指标，包括：
- 全年电量统计
- 2次循环天数统计
- 1次循环次数统计
- DOD统计（≤50%, 50%-70%, 70%-90%, >90%）
- 其他年度性能指标
"""

import pandas as pd
from typing import Dict, Any, Optional
import warnings


class SummaryIndicatorsCalculator:
    """汇总指标计算器"""

    def __init__(self):
        """初始化计算器"""
        pass

    def calculate_annual_summary(self, daily_results: pd.DataFrame,
                               capacity_results: Dict[str, Any],
                               config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        计算年度汇总指标

        Args:
            daily_results: 每日计算结果
            capacity_results: 装机容量计算结果
            config_params: 配置参数

        Returns:
            Dict[str, Any]: 年度汇总指标
        """
        if daily_results.empty:
            return self._get_empty_summary()

        summary = {}

        # 基本统计
        summary['计算天数'] = len(daily_results)
        summary['数据起始日期'] = daily_results['日期'].min()
        summary['数据结束日期'] = daily_results['日期'].max()

        # 1. 全年电量（年度汇总每日总放电量=sum（循环1+循环2））
        total_annual_electricity = daily_results['总放电'].sum()
        summary['全年电量_kwh'] = total_annual_electricity
        summary['全年电量_万kwh'] = total_annual_electricity / 10000

        # 2. 2次循环天数（年度汇总两充两放的天数=sum(两充两放)）
        two_cycle_days = daily_results['两充两放'].sum()
        summary['2次循环天数'] = two_cycle_days

        # 3. 1次循环次数
        # 统计年度范围内循环1计数和循环2计数中值为100%的单元格数量，然后减去（2次循环天数乘以2）的结果
        cycle1_100_count = (daily_results['循环1计数'] >= 0.99).sum()
        cycle2_100_count = (daily_results['循环2计数'] >= 0.99).sum()
        total_100_cycles = cycle1_100_count + cycle2_100_count
        one_cycle_count = total_100_cycles - (two_cycle_days * 2)
        summary['1次循环次数'] = max(one_cycle_count, 0)

        # 4-7. DOD统计
        summary.update(self._calculate_dod_statistics(daily_results))

        # 8. 折算天数汇总
        total_equivalent_days = daily_results['折算天数'].sum()
        summary['折算天数汇总'] = total_equivalent_days

        # 9. 全年放电量（万kWh，考虑效率）
        annual_discharge_with_efficiency = total_annual_electricity / 10000 * 0.93
        summary['全年放电量_万kwh_含效率'] = annual_discharge_with_efficiency

        # 10. 总利润（日收益汇总加和成总收益）
        total_profit = daily_results['总收益'].sum()
        summary['总利润'] = total_profit

        # 11. 平均日收益
        avg_daily_revenue = daily_results['总收益'].mean()
        summary['平均日收益'] = avg_daily_revenue

        # 12. 最大日收益
        max_daily_revenue = daily_results['总收益'].max()
        summary['最大日收益'] = max_daily_revenue

        # 13. 最小日收益
        min_daily_revenue = daily_results['总收益'].min()
        summary['最小日收益'] = min_daily_revenue

        # 14. 尖放电量统计
        total_sharp_discharge = daily_results['尖放电'].sum()
        summary['总尖放电量'] = total_sharp_discharge

        # 15. 尖放电占比
        if total_annual_electricity > 0:
            sharp_discharge_ratio = total_sharp_discharge / total_annual_electricity
        else:
            sharp_discharge_ratio = 0
        summary['尖放电占比'] = sharp_discharge_ratio

        # 16. 平均充电容量
        avg_charging_capacity = daily_results['充电容量'].mean()
        summary['平均充电容量'] = avg_charging_capacity

        # 17. 平均系统留存
        avg_system_retention = daily_results['系统留存'].mean()
        summary['平均系统留存'] = avg_system_retention

        # 18. 满容量天数统计
        total_full_capacity_days = daily_results['满容量天数'].sum()
        summary['满容量天数汇总'] = total_full_capacity_days

        # 19. 月度统计
        summary['月度统计'] = self._calculate_monthly_statistics(daily_results)

        # 20. 系统参数信息
        summary['系统参数'] = {
            '系统容量_kw': capacity_results.get('系统容量_kw', 200),
            '系统电量_kwh': capacity_results.get('系统电量_kwh', 400),
            '变压器容量_kva': capacity_results.get('变压器容量_kva', 2600),
            'DOD百分比': capacity_results.get('dod_percentage', 0.95),
            '峰1放电倍率': capacity_results.get('峰1放电倍率', 0.5),
        }

        return summary

    def _calculate_dod_statistics(self, daily_results: pd.DataFrame) -> Dict[str, Any]:
        """
        计算DOD统计

        Args:
            daily_results: 每日计算结果

        Returns:
            Dict[str, Any]: DOD统计结果
        """
        dod_stats = {}

        # 合并循环1计数和循环2计数
        all_cycle_counts = []
        all_cycle_counts.extend(daily_results['循环1计数'].tolist())
        all_cycle_counts.extend(daily_results['循环2计数'].tolist())

        # 过滤掉NaN值
        all_cycle_counts = [x for x in all_cycle_counts if pd.notna(x)]

        if not all_cycle_counts:
            return {
                'DOD≤50%次数': 0,
                '50%<DOD≤70%次数': 0,
                '70%<DOD≤90%次数': 0,
                '90%<DOD次数': 0
            }

        # 统计各DOD范围的次数
        dod_50_count = sum(1 for x in all_cycle_counts if x <= 0.5)
        dod_50_70_count = sum(1 for x in all_cycle_counts if 0.5 < x <= 0.7)
        dod_70_90_count = sum(1 for x in all_cycle_counts if 0.7 < x <= 0.9)
        dod_90_count = sum(1 for x in all_cycle_counts if x > 0.9)

        dod_stats.update({
            'DOD≤50%次数': dod_50_count,
            '50%<DOD≤70%次数': dod_50_70_count,
            '70%<DOD≤90%次数': dod_70_90_count,
            '90%<DOD次数': dod_90_count
        })

        return dod_stats

    def _calculate_monthly_statistics(self, daily_results: pd.DataFrame) -> Dict[int, Dict[str, Any]]:
        """
        计算月度统计

        Args:
            daily_results: 每日计算结果

        Returns:
            Dict[int, Dict[str, Any]]: 月度统计结果
        """
        monthly_stats = {}

        if '月份' not in daily_results.columns:
            return monthly_stats

        for month in daily_results['月份'].unique():
            month_data = daily_results[daily_results['月份'] == month]

            if month_data.empty:
                continue

            month_stat = {
                '天数': len(month_data),
                '总放电量': month_data['总放电'].sum(),
                '平均日放电量': month_data['总放电'].mean(),
                '总收益': month_data['总收益'].sum(),
                '平均日收益': month_data['总收益'].mean(),
                '最大日收益': month_data['总收益'].max(),
                '最小日收益': month_data['总收益'].min(),
                '尖放电量': month_data['尖放电'].sum(),
                '两充两放天数': month_data['两充两放'].sum(),
                '折算天数': month_data['折算天数'].sum(),
                '平均充电容量': month_data['充电容量'].mean(),
            }

            # 计算月度尖放电占比
            if month_stat['总放电量'] > 0:
                month_stat['尖放电占比'] = month_stat['尖放电量'] / month_stat['总放电量']
            else:
                month_stat['尖放电占比'] = 0

            monthly_stats[month] = month_stat

        return monthly_stats

    def _get_empty_summary(self) -> Dict[str, Any]:
        """
        获取空的汇总结果

        Returns:
            Dict[str, Any]: 空的汇总结果
        """
        return {
            '计算天数': 0,
            '全年电量_kwh': 0,
            '全年电量_万kwh': 0,
            '2次循环天数': 0,
            '1次循环次数': 0,
            'DOD≤50%次数': 0,
            '50%<DOD≤70%次数': 0,
            '70%<DOD≤90%次数': 0,
            '90%<DOD次数': 0,
            '折算天数汇总': 0,
            '全年放电量_万kwh_含效率': 0,
            '总利润': 0,
            '平均日收益': 0,
            '最大日收益': 0,
            '最小日收益': 0,
            '总尖放电量': 0,
            '尖放电占比': 0,
            '平均充电容量': 0,
            '平均系统留存': 0,
            '满容量天数汇总': 0,
            '月度统计': {},
            '系统参数': {}
        }

    def get_summary_report(self, summary: Dict[str, Any]) -> str:
        """
        生成汇总报告

        Args:
            summary: 汇总结果

        Returns:
            str: 汇总报告文本
        """
        report_lines = [
            "储能系统年度汇总报告",
            "=" * 50,
            f"计算天数: {summary.get('计算天数', 0)}",
            f"数据时间范围: {summary.get('数据起始日期', 'N/A')} 至 {summary.get('数据结束日期', 'N/A')}",
            "",
            "电量统计:",
            f"  全年电量: {summary.get('全年电量_万kwh', 0):.2f} 万kWh",
            f"  全年放电量(含效率): {summary.get('全年放电量_万kwh_含效率', 0):.2f} 万kWh",
            f"  总尖放电量: {summary.get('总尖放电量', 0):.2f} kWh",
            f"  尖放电占比: {summary.get('尖放电占比', 0):.2%}",
            "",
            "循环统计:",
            f"  2次循环天数: {summary.get('2次循环天数', 0)}",
            f"  1次循环次数: {summary.get('1次循环次数', 0)}",
            f"  折算天数汇总: {summary.get('折算天数汇总', 0):.2f}",
            "",
            "DOD统计:",
            f"  DOD≤50%次数: {summary.get('DOD≤50%次数', 0)}",
            f"  50%<DOD≤70%次数: {summary.get('50%<DOD≤70%次数', 0)}",
            f"  70%<DOD≤90%次数: {summary.get('70%<DOD≤90%次数', 0)}",
            f"  90%<DOD次数: {summary.get('90%<DOD次数', 0)}",
            "",
            "收益统计:",
            f"  总利润: {summary.get('总利润', 0):.2f} 元",
            f"  平均日收益: {summary.get('平均日收益', 0):.2f} 元",
            f"  最大日收益: {summary.get('最大日收益', 0):.2f} 元",
            f"  最小日收益: {summary.get('最小日收益', 0):.2f} 元",
            "",
            "系统参数:",
        ]

        system_params = summary.get('系统参数', {})
        for key, value in system_params.items():
            report_lines.append(f"  {key}: {value}")

        return "\n".join(report_lines)


def calculate_summary_indicators(daily_results: pd.DataFrame,
                               capacity_results: Dict[str, Any],
                               config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    计算汇总指标（主要接口函数）

    Args:
        daily_results: 每日计算结果
        capacity_results: 装机容量计算结果
        config_params: 配置参数

    Returns:
        Dict[str, Any]: 汇总指标结果
    """
    calculator = SummaryIndicatorsCalculator()
    return calculator.calculate_annual_summary(daily_results, capacity_results, config_params)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from 储能装机容量测算 import load_excel_data, calculate_energy_storage_capacity
    from 基本电费设定 import calculate_basic_electricity_fees
    from 容量负荷测算 import calculate_capacity_load_metrics

    file_path = "data/(厂商机密)储能容量测算模型.xlsx"

    try:
        # 加载数据
        load_data, price_data = load_excel_data(file_path)

        # 计算装机容量
        capacity_results = calculate_energy_storage_capacity(load_data, price_data)

        # 计算基本电费
        fee_results = calculate_basic_electricity_fees(load_data, capacity_results)

        # 计算容量负荷指标
        daily_results = calculate_capacity_load_metrics(
            load_data, capacity_results, fee_results
        )

        # 计算汇总指标
        summary_results = calculate_summary_indicators(daily_results, capacity_results)

        # 生成报告
        calculator = SummaryIndicatorsCalculator()
        report = calculator.get_summary_report(summary_results)

        print(report)

    except Exception as e:
        print(f"计算失败: {str(e)}")