#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能装机容量测算模块
Energy Storage Installed Capacity Calculation Module

本模块负责计算储能系统的装机容量相关参数，包括：
- 变压器容量
- 基本电费类型
- 功率因素
- 功率上限
- 系统容量和系统电量
"""

import pandas as pd
from typing import Dict, Any, Optional, Tuple
import warnings


class EnergyStorageCapacityCalculator:
    """储能装机容量计算器"""

    def __init__(self):
        """初始化计算器"""
        self.default_params = {
            'transformer_capacity_kva': 1000,  # 变压器容量(kVA)
            'basic_fee_type': '实际',  # 基本电费类型：按容、需量、实际
            'power_factor': 0.92,  # 功率因素
            'system_capacity_kw': 200,  # 系统容量(kW)
            'system_energy_kwh': 400,  # 系统电量(kWh)
            'dod_percentage': 0.95,  # DOD百分比
            'daily_decay_rate': 0.000082,  # 日衰减率
            'annual_decay_rate': 0.03,  # 年衰减率
            'peak1_discharge_rate': 0.5,  # 峰1放电倍率

            # 电价参数
            'sharp_peak_price': 1.2885,  # 尖峰电价(元/kWh)
            'peak_price': 1.0737,  # 高峰电价(元/kWh)
            'flat_price': 0.6244,  # 平段电价(元/kWh)
            'valley_price': 0.2613,  # 低谷电价(元/kWh)
            'demand_price': 48.0,  # 需量电价(元/kW)
            'capacity_price': 30.0,  # 按容电价(元/kVA)
            'power_agency_price': 0.05,  # 电力代理价(元/kWh)
            'transmission_price': 0.15,  # 输配电价(元/kWh)
            'additional_price': 0.02,  # 附加费 (元/kWh)

            # 电价比例参数
            'peak_ratio': 0.7196,  # 高峰比例
            'sharp_peak_ratio': 0.2,  # 尖峰比例
            'valley_ratio': 0.5815,  # 低谷比例
        }

    def load_data_from_platform(self, load_data: pd.DataFrame, price_data: pd.DataFrame,
                               config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从数据平台加载数据并计算装机容量参数

        Args:
            load_data: 负荷原始清单数据
            price_data: 基础参数电价数据（已弃用，保留兼容性）
            config_params: 配置参数字典（包含电价信息）

        Returns:
            Dict[str, Any]: 计算结果字典
        """
        # 合并默认参数和用户参数
        params = self.default_params.copy()
        if config_params:
            params.update(config_params)

        # 处理负荷数据
        processed_load_data = self._process_load_data(load_data)

        # 处理电价数据（现在从配置参数获取）
        processed_price_data = self._process_price_data_from_params(params)

        # 计算装机容量参数
        capacity_results = self._calculate_capacity_parameters(
            processed_load_data, processed_price_data, params
        )

        return capacity_results

    def _process_load_data(self, load_data: pd.DataFrame) -> pd.DataFrame:
        """
        处理负荷原始清单数据

        Args:
            load_data: 原始负荷数据

        Returns:
            pd.DataFrame: 处理后的负荷数据
        """
        df = load_data.copy()

        # 清理列名和数据
        if '用户名称' in df.columns and '日期' in df.columns and '瞬时有功' in df.columns:
            # 提取时间信息（从Unnamed: 2列）
            if 'Unnamed: 2' in df.columns:
                df['时间'] = df['Unnamed: 2'].astype(str).str.replace('\t', '').str.strip()

            # 确保日期格式正确
            df['日期'] = pd.to_datetime(df['日期'])
            df['月份'] = df['日期'].dt.month

            # 清理瞬时有功功率数据
            df['瞬时有功'] = pd.to_numeric(df['瞬时有功'], errors='coerce')

            # 过滤有效数据
            df = df.dropna(subset=['瞬时有功'])

            # 按日期和时间排序
            df = df.sort_values(['日期', '时间'])

        else:
            raise ValueError("负荷数据缺少必要的列：用户名称、日期、瞬时有功")

        return df

    def _process_price_data_from_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        从配置参数中处理电价数据

        Args:
            params: 配置参数字典，包含所有电价信息

        Returns:
            Dict[str, Any]: 处理后的电价参数
        """
        price_info = {
            # 基本电价信息
            '尖峰电价': params.get('sharp_peak_price', 1.29),
            '高峰电价': params.get('peak_price', 1.07),
            '平段电价': params.get('flat_price', 0.62),
            '低谷电价': params.get('valley_price', 0.26),
            '需量电价': params.get('demand_price', 43.2),
            '按容电价': params.get('capacity_price', 30.0),
            '电力代理价': params.get('power_agency_price', 0.05),
            '输配': params.get('transmission_price', 0.15),
            '附加': params.get('additional_price', 0.02),

            # 电价比例
            '高峰比例': params.get('peak_ratio', 0.7196),
            '尖峰比例': params.get('sharp_peak_ratio', 0.2),
            '低谷比例': params.get('valley_ratio', 0.5815),

            # 月度电价（使用基本电价作为默认值）
            '月度电价': self._generate_monthly_prices_from_params(params)
        }

        return price_info

    def _generate_monthly_prices_from_params(self, params: Dict[str, Any]) -> Dict[int, Dict[str, Any]]:
        """
        从参数生成月度电价信息

        Args:
            params: 配置参数字典

        Returns:
            Dict[int, Dict[str, Any]]: 月度电价字典
        """
        # 基本电价
        base_sharp_price = params.get('sharp_peak_price', 1.29)
        base_peak_price = params.get('peak_price', 1.07)
        base_flat_price = params.get('flat_price', 0.62)
        base_valley_price = params.get('valley_price', 0.26)

        monthly_prices = {}

        # 为每个月生成电价信息
        for month in range(1, 13):
            # 可以根据需要添加月度浮动逻辑，这里使用基本电价
            monthly_prices[month] = {
                '分类': '正常',  # 默认为正常电价
                '天数': 30,  # 默认天数
                '尖峰电价': base_sharp_price,
                '高峰电价': base_peak_price,
                '平段电价': base_flat_price,
                '低谷电价': base_valley_price,
            }

            # 特殊月份可以设置为特殊电价（如夏季）
            if month in [7, 8, 9]:  # 7-9月为特殊电价月份
                monthly_prices[month]['分类'] = '特殊'

        return monthly_prices

    def _process_price_data(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        处理基础参数电价数据

        Args:
            price_data: 原始电价数据

        Returns:
            Dict[str, Any]: 处理后的电价参数
        """
        price_info = {}

        try:
            # 提取基本电价信息（第一行是标题，第二行是数据）
            if len(price_data) >= 2:
                headers = price_data.iloc[0].tolist()
                values = price_data.iloc[1].tolist()

                # 创建基本电价字典
                for i, header in enumerate(headers):
                    if header and not pd.isna(header) and i < len(values):
                        if header in ['高峰比例', '尖峰比例', '低谷比例', '电力代理价',
                                    '输配', '附加', '尖峰电价', '高峰电价', '平段电价', '低谷电价']:
                            price_info[header] = values[i] if not pd.isna(values[i]) else 0

                # 提取月度电价浮动信息
                monthly_prices = {}
                for i in range(5, len(price_data)):  # 从第6行开始是月度数据
                    row = price_data.iloc[i]
                    if not pd.isna(row.iloc[0]) and isinstance(row.iloc[0], (int, float)):
                        month = int(row.iloc[0])
                        monthly_prices[month] = {
                            '分类': row.iloc[1] if not pd.isna(row.iloc[1]) else '正常',
                            '天数': row.iloc[2] if not pd.isna(row.iloc[2]) else 30,
                            '尖峰电价': row.iloc[7] if not pd.isna(row.iloc[7]) else price_info.get('尖峰电价', 1.29),
                            '高峰电价': row.iloc[8] if not pd.isna(row.iloc[8]) else price_info.get('高峰电价', 1.07),
                            '平段电价': row.iloc[9] if not pd.isna(row.iloc[9]) else price_info.get('平段电价', 0.62),
                            '低谷电价': row.iloc[10] if not pd.isna(row.iloc[10]) else price_info.get('低谷电价', 0.26),
                        }

                price_info['月度电价'] = monthly_prices

        except Exception as e:
            warnings.warn(f"电价数据处理失败，使用默认值: {str(e)}")
            # 使用默认电价
            price_info = {
                '高峰比例': 0.7196,
                '尖峰比例': 0.2,
                '低谷比例': 0.5815,
                '尖峰电价': 1.29,
                '高峰电价': 1.07,
                '平段电价': 0.62,
                '低谷电价': 0.26,
                '月度电价': {}
            }

        return price_info

    def _calculate_capacity_parameters(self, load_data: pd.DataFrame,
                                     price_data: Dict[str, Any],
                                     params: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算装机容量相关参数

        Args:
            load_data: 处理后的负荷数据
            price_data: 处理后的电价数据
            params: 配置参数

        Returns:
            Dict[str, Any]: 计算结果
        """
        results = {}

        # 1. 变压器容量（从参数获取）
        results['变压器容量_kva'] = params['transformer_capacity_kva']

        # 2. 基本电费类型（从参数获取）
        results['基本电费类型'] = params['basic_fee_type']

        # 3. 功率因素（从参数获取）
        results['功率因素'] = params['power_factor']

        # 4. 计算功率上限（根据月份和基本电费类型）
        results['功率上限_kw'] = self._calculate_power_limit(
            load_data, params, results['基本电费类型']
        )

        # 5. 系统容量（从参数获取，需要手动填写）
        results['系统容量_kw'] = params['system_capacity_kw']

        # 6. 系统电量（等于系统容量的2倍）
        results['系统电量_kwh'] = params['system_capacity_kw'] * 2

        # 7-10. DOD统计（需要后续计算循环数据后统计）
        results['dod_50_count'] = 0  # DOD≤50%次数
        results['dod_50_70_count'] = 0  # 50%<DOD≤70%次数
        results['dod_70_90_count'] = 0  # 70%<DOD≤90%次数
        results['dod_90_count'] = 0  # 90%<DOD次数

        # 11. 峰1放电倍率（从参数获取）
        results['峰1放电倍率'] = params['peak1_discharge_rate']

        # 12-14. 这些指标需要在容量负荷测算模块中计算
        results['折算天数'] = 0  # 日折算天数汇总加和
        results['全年放电量_万kwh'] = 0  # 年度汇总每日总放电量/10000*0.93
        results['总利润'] = 0  # 日收益汇总加和成总收益

        # 添加其他配置参数
        results['dod_percentage'] = params['dod_percentage']
        results['daily_decay_rate'] = params['daily_decay_rate']
        results['annual_decay_rate'] = params['annual_decay_rate']

        # 添加电价信息
        results['电价信息'] = price_data

        return results

    def _calculate_power_limit(self, load_data: pd.DataFrame,
                             params: Dict[str, Any],
                             fee_type: str) -> float:
        """
        计算功率上限

        Args:
            load_data: 负荷数据
            params: 参数字典
            fee_type: 基本电费类型

        Returns:
            float: 功率上限(kW)
        """
        transformer_capacity = params['transformer_capacity_kva']
        power_factor = params['power_factor']

        if fee_type == '按容':
            # 如果按容计费，功率上限 = 变压器容量 * 功率因素
            power_limit = transformer_capacity * power_factor
        elif fee_type == '按需':
            # 如果按需计费，需要计算最大需量
            if not load_data.empty:
                # 计算每月最大瞬时功率
                monthly_max = load_data.groupby('月份')['瞬时有功'].max()
                # 取最大值作为功率上限
                max_power = monthly_max.max() if not monthly_max.empty else 0
                power_limit = max(max_power * power_factor, transformer_capacity * power_factor)
            else:
                power_limit = transformer_capacity * power_factor
        else:  # 实际
            # 实际情况下，功率上限等于变压器容量 * 功率因素
            power_limit = transformer_capacity * power_factor

        return power_limit

    def get_capacity_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取装机容量计算摘要

        Args:
            results: 计算结果

        Returns:
            Dict[str, Any]: 摘要信息
        """
        summary = {
            '变压器容量(kVA)': results.get('变压器容量_kva', 0),
            '基本电费类型': results.get('基本电费类型', '实际'),
            '功率因素': results.get('功率因素', 0.92),
            '功率上限(kW)': results.get('功率上限_kw', 0),
            '系统容量(kW)': results.get('系统容量_kw', 200),
            '系统电量(kWh)': results.get('系统电量_kwh', 400),
            'DOD百分比': results.get('dod_percentage', 0.95),
            '峰1放电倍率': results.get('峰1放电倍率', 0.5),
        }
        return summary


def load_excel_data(file_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    从Excel文件加载数据

    Args:
        file_path: Excel文件路径

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (负荷数据, 电价数据)
    """
    try:
        # 读取负荷原始清单
        load_data = pd.read_excel(file_path, sheet_name='负荷-原始清单')

        # 读取基础参数电价
        # price_data = pd.read_excel(file_path, sheet_name='基础参数-电价')

        return load_data

    except Exception as e:
        raise ValueError(f"读取Excel文件失败: {str(e)}")


def calculate_energy_storage_capacity(load_data: pd.DataFrame,
                                    config_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    计算储能装机容量（主要接口函数）

    Args:
        load_data: 负荷原始清单数据
        price_data: 基础参数电价数据
        config_params: 配置参数字典

    Returns:
        Dict[str, Any]: 计算结果
    """
    calculator = EnergyStorageCapacityCalculator()
    return calculator.load_data_from_platform(load_data=load_data, config_params=config_params)


if __name__ == "__main__":
    # 测试代码
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"

    try:
        # 加载数据
        load_data= load_excel_data(file_path)

        # 计算装机容量
        results = calculate_energy_storage_capacity(load_data)

        # 显示摘要
        calculator = EnergyStorageCapacityCalculator()
        summary = calculator.get_capacity_summary(results)

        print("储能装机容量计算结果:")
        print("=" * 40)
        for key, value in summary.items():
            print(f"{key}: {value}")

    except Exception as e:
        print(f"计算失败: {str(e)}")