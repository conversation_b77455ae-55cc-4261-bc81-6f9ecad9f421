#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能系统完整测试模块
Complete Energy Storage System Test Module

本模块提供完整的系统测试，验证所有四个模块的集成工作：
1. 单元测试
2. 集成测试
3. 性能测试
4. 数据验证测试
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import unittest
import warnings
import time
import os

# 导入所有模块
from 储能装机容量测算 import EnergyStorageCapacityCalculator, load_excel_data
from 基本电费设定 import BasicElectricityFeeCalculator
from 容量负荷测算 import CapacityLoadCalculator
from 汇总指标 import SummaryIndicatorsCalculator
from 储能系统集成计算 import EnergyStorageSystemCalculator, main_calculation_pipeline


class TestEnergyStorageSystem(unittest.TestCase):
    """储能系统测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_file_path = "data/(厂商机密)储能容量测算模型.xlsx"
        self.test_config = {
            'system_capacity_kw': 200,
            'system_energy_kwh': 400,
            'transformer_capacity_kva': 2600,
            'power_factor': 0.92,
            'dod_percentage': 0.95,
            'daily_decay_rate': 0.000082,
            'peak1_discharge_rate': 0.5,
        }
        
        # 检查测试文件是否存在
        if not os.path.exists(self.test_file_path):
            self.skipTest(f"测试文件不存在: {self.test_file_path}")
    
    def test_01_data_loading(self):
        """测试数据加载"""
        print("\n测试1: 数据加载")
        
        try:
            load_data, price_data = load_excel_data(self.test_file_path)
            
            # 验证数据不为空
            self.assertFalse(load_data.empty, "负荷数据不应为空")
            self.assertFalse(price_data.empty, "电价数据不应为空")
            
            # 验证必要列存在
            required_load_columns = ['用户名称', '日期', '瞬时有功']
            for col in required_load_columns:
                self.assertIn(col, load_data.columns, f"负荷数据缺少列: {col}")
            
            print(f"✓ 负荷数据加载成功: {load_data.shape}")
            print(f"✓ 电价数据加载成功: {price_data.shape}")
            
        except Exception as e:
            self.fail(f"数据加载失败: {str(e)}")
    
    def test_02_capacity_calculation(self):
        """测试装机容量计算"""
        print("\n测试2: 装机容量计算")
        
        try:
            load_data, price_data = load_excel_data(self.test_file_path)
            
            calculator = EnergyStorageCapacityCalculator()
            results = calculator.load_data_from_platform(load_data, price_data, self.test_config)
            
            # 验证关键结果
            self.assertIn('变压器容量_kva', results)
            self.assertIn('系统容量_kw', results)
            self.assertIn('系统电量_kwh', results)
            self.assertIn('功率上限_kw', results)
            
            # 验证数值合理性
            self.assertGreater(results['变压器容量_kva'], 0)
            self.assertGreater(results['系统容量_kw'], 0)
            self.assertGreater(results['系统电量_kwh'], 0)
            
            print(f"✓ 装机容量计算成功")
            print(f"  变压器容量: {results['变压器容量_kva']} kVA")
            print(f"  系统容量: {results['系统容量_kw']} kW")
            print(f"  系统电量: {results['系统电量_kwh']} kWh")
            
        except Exception as e:
            self.fail(f"装机容量计算失败: {str(e)}")
    
    def test_03_fee_calculation(self):
        """测试基本电费计算"""
        print("\n测试3: 基本电费计算")
        
        try:
            load_data, price_data = load_excel_data(self.test_file_path)
            
            # 先计算装机容量
            capacity_calculator = EnergyStorageCapacityCalculator()
            capacity_results = capacity_calculator.load_data_from_platform(
                load_data, price_data, self.test_config
            )
            
            # 计算基本电费
            fee_calculator = BasicElectricityFeeCalculator()
            fee_results = fee_calculator.calculate_monthly_fees(
                load_data, capacity_results, self.test_config
            )
            
            # 验证结果结构
            self.assertIn('月度结果', fee_results)
            self.assertIn('年度汇总', fee_results)
            
            monthly_results = fee_results['月度结果']
            self.assertGreater(len(monthly_results), 0, "应该有月度结果")
            
            print(f"✓ 基本电费计算成功")
            print(f"  计算月份数: {len(monthly_results)}")
            
        except Exception as e:
            self.fail(f"基本电费计算失败: {str(e)}")
    
    def test_04_capacity_load_calculation(self):
        """测试容量负荷计算"""
        print("\n测试4: 容量负荷计算")
        
        try:
            load_data, price_data = load_excel_data(self.test_file_path)
            
            # 前置计算
            capacity_calculator = EnergyStorageCapacityCalculator()
            capacity_results = capacity_calculator.load_data_from_platform(
                load_data, price_data, self.test_config
            )
            
            fee_calculator = BasicElectricityFeeCalculator()
            fee_results = fee_calculator.calculate_monthly_fees(
                load_data, capacity_results, self.test_config
            )
            
            # 容量负荷计算
            load_calculator = CapacityLoadCalculator()
            daily_results = load_calculator.calculate_daily_metrics(
                load_data, capacity_results, fee_results, self.test_config
            )
            
            # 验证结果
            self.assertFalse(daily_results.empty, "每日计算结果不应为空")
            
            # 验证关键列存在
            required_columns = ['日期', '月份', '充电容量', '总放电', '总收益']
            for col in required_columns:
                self.assertIn(col, daily_results.columns, f"缺少列: {col}")
            
            print(f"✓ 容量负荷计算成功")
            print(f"  计算天数: {len(daily_results)}")
            print(f"  总收益: {daily_results['总收益'].sum():.2f}")
            
        except Exception as e:
            self.fail(f"容量负荷计算失败: {str(e)}")
    
    def test_05_summary_calculation(self):
        """测试汇总指标计算"""
        print("\n测试5: 汇总指标计算")
        
        try:
            # 执行完整计算流程
            calculator = EnergyStorageSystemCalculator()
            results = calculator.calculate_from_excel_file(self.test_file_path, self.test_config)
            
            # 验证汇总指标
            summary_results = results['汇总指标结果']
            
            required_indicators = [
                '计算天数', '全年电量_万kwh', '总利润', '平均日收益',
                '2次循环天数', '1次循环次数', 'DOD≤50%次数'
            ]
            
            for indicator in required_indicators:
                self.assertIn(indicator, summary_results, f"缺少指标: {indicator}")
            
            print(f"✓ 汇总指标计算成功")
            print(f"  计算天数: {summary_results['计算天数']}")
            print(f"  全年电量: {summary_results['全年电量_万kwh']:.2f} 万kWh")
            print(f"  总利润: {summary_results['总利润']:.2f} 元")
            
        except Exception as e:
            self.fail(f"汇总指标计算失败: {str(e)}")
    
    def test_06_integration_test(self):
        """测试完整集成"""
        print("\n测试6: 完整集成测试")
        
        try:
            start_time = time.time()
            
            # 执行完整计算流程
            results = main_calculation_pipeline(
                self.test_file_path, 
                config_params=self.test_config,
                output_file="test_output.xlsx"
            )
            
            end_time = time.time()
            calculation_time = end_time - start_time
            
            # 验证输出表
            output_tables = results['输出表']
            expected_tables = ['装机容量和基本电费汇总表', '每日详细计算结果表', '年度汇总指标表']
            
            for table_name in expected_tables:
                self.assertIn(table_name, output_tables, f"缺少输出表: {table_name}")
                self.assertFalse(output_tables[table_name].empty, f"输出表为空: {table_name}")
            
            print(f"✓ 完整集成测试成功")
            print(f"  计算耗时: {calculation_time:.2f} 秒")
            print(f"  输出表数量: {len(output_tables)}")
            
            # 清理测试文件
            if os.path.exists("test_output.xlsx"):
                os.remove("test_output.xlsx")
            
        except Exception as e:
            self.fail(f"完整集成测试失败: {str(e)}")
    
    def test_07_data_validation(self):
        """测试数据验证"""
        print("\n测试7: 数据验证测试")
        
        try:
            calculator = EnergyStorageSystemCalculator()
            results = calculator.calculate_from_excel_file(self.test_file_path, self.test_config)
            
            daily_results = results['每日计算结果']
            summary_results = results['汇总指标结果']
            
            # 验证数据一致性
            calculated_total_discharge = daily_results['总放电'].sum()
            summary_total_discharge = summary_results['全年电量_kwh']
            
            # 允许小的数值误差
            self.assertAlmostEqual(
                calculated_total_discharge, summary_total_discharge, 
                delta=1.0, msg="总放电量数据不一致"
            )
            
            # 验证收益计算
            calculated_total_revenue = daily_results['总收益'].sum()
            summary_total_revenue = summary_results['总利润']
            
            self.assertAlmostEqual(
                calculated_total_revenue, summary_total_revenue,
                delta=0.01, msg="总收益数据不一致"
            )
            
            print(f"✓ 数据验证测试通过")
            print(f"  总放电量一致性: ✓")
            print(f"  总收益一致性: ✓")
            
        except Exception as e:
            self.fail(f"数据验证测试失败: {str(e)}")


def run_performance_test():
    """运行性能测试"""
    print("\n" + "="*50)
    print("性能测试")
    print("="*50)
    
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print("测试文件不存在，跳过性能测试")
        return
    
    try:
        start_time = time.time()
        
        # 执行多次计算测试性能
        for i in range(3):
            print(f"第 {i+1} 次计算...")
            results = main_calculation_pipeline(file_path)
            
        end_time = time.time()
        avg_time = (end_time - start_time) / 3
        
        print(f"\n性能测试结果:")
        print(f"平均计算时间: {avg_time:.2f} 秒")
        print(f"计算天数: {results['汇总指标结果']['计算天数']}")
        print(f"每天平均计算时间: {avg_time / results['汇总指标结果']['计算天数'] * 1000:.2f} 毫秒")
        
    except Exception as e:
        print(f"性能测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("储能系统完整测试")
    print("="*50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n所有测试完成!")


if __name__ == "__main__":
    main()
