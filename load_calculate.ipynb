{"cells": [{"cell_type": "code", "execution_count": 1, "id": "52d39637", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "d2ac8128", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>用户名称</th>\n", "      <th>日期</th>\n", "      <th>Unnamed: 2</th>\n", "      <th>资产编号</th>\n", "      <th>瞬时有功</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:30:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:15:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:00:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>22:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33142</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2023-07-31</td>\n", "      <td>01:00:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33143</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2023-07-31</td>\n", "      <td>00:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33144</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2023-07-31</td>\n", "      <td>00:30:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33145</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2023-07-31</td>\n", "      <td>00:15:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33146</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2023-07-31</td>\n", "      <td>00:00:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>33147 rows × 5 columns</p>\n", "</div>"], "text/plain": ["               用户名称         日期  Unnamed: 2                   资产编号  瞬时有功\n", "0      杭州富阳永顺工贸有限公司 2022-08-01  23:45:00\\t  00010030075657(电能表)\\t   7.2\n", "1      杭州富阳永顺工贸有限公司 2022-08-01  23:30:00\\t  00010030075657(电能表)\\t   6.0\n", "2      杭州富阳永顺工贸有限公司 2022-08-01  23:15:00\\t  00010030075657(电能表)\\t   6.3\n", "3      杭州富阳永顺工贸有限公司 2022-08-01  23:00:00\\t  00010030075657(电能表)\\t   6.9\n", "4      杭州富阳永顺工贸有限公司 2022-08-01  22:45:00\\t  00010030075657(电能表)\\t   7.5\n", "...             ...        ...         ...                    ...   ...\n", "33142  杭州富阳永顺工贸有限公司 2023-07-31  01:00:00\\t  00010030075657(电能表)\\t   7.5\n", "33143  杭州富阳永顺工贸有限公司 2023-07-31  00:45:00\\t  00010030075657(电能表)\\t   7.5\n", "33144  杭州富阳永顺工贸有限公司 2023-07-31  00:30:00\\t  00010030075657(电能表)\\t   7.2\n", "33145  杭州富阳永顺工贸有限公司 2023-07-31  00:15:00\\t  00010030075657(电能表)\\t   7.5\n", "33146  杭州富阳永顺工贸有限公司 2023-07-31  00:00:00\\t  00010030075657(电能表)\\t   7.5\n", "\n", "[33147 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_excel('./data/(厂商机密)储能容量测算模型.xlsx',sheet_name='负荷-原始清单')\n", "data.dropna(axis=1, how='all', inplace=True)\n", "data.iloc[:,:-3]\n"]}, {"cell_type": "code", "execution_count": 3, "id": "be0fd55f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>用户名称</th>\n", "      <th>日期</th>\n", "      <th>Unnamed: 2</th>\n", "      <th>资产编号</th>\n", "      <th>瞬时有功</th>\n", "      <th>瞬时有功.1</th>\n", "      <th>日期.1</th>\n", "      <th>户名</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.2</td>\n", "      <td>0.1536</td>\n", "      <td>2022-01-21 13:15:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:30:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.0</td>\n", "      <td>0.1551</td>\n", "      <td>2022-01-21 13:00:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:15:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.3</td>\n", "      <td>0.1461</td>\n", "      <td>2022-01-21 12:45:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>23:00:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.9</td>\n", "      <td>0.1505</td>\n", "      <td>2022-01-21 12:30:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>22:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.5</td>\n", "      <td>0.0984</td>\n", "      <td>2022-01-21 12:15:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>22:30:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.9</td>\n", "      <td>0.0586</td>\n", "      <td>2022-01-21 12:00:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>22:15:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>6.6</td>\n", "      <td>0.0693</td>\n", "      <td>2022-01-21 11:45:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>22:00:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>8.1</td>\n", "      <td>0.0687</td>\n", "      <td>2022-01-21 11:30:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>21:45:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>8.1</td>\n", "      <td>0.0687</td>\n", "      <td>2022-01-21 11:15:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>杭州富阳永顺工贸有限公司</td>\n", "      <td>2022-08-01</td>\n", "      <td>21:30:00\\t</td>\n", "      <td>00010030075657(电能表)\\t</td>\n", "      <td>7.8</td>\n", "      <td>0.1597</td>\n", "      <td>2022-01-21 11:00:00\\t</td>\n", "      <td>XXX有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           用户名称         日期  Unnamed: 2                   资产编号  瞬时有功  瞬时有功.1  \\\n", "0  杭州富阳永顺工贸有限公司 2022-08-01  23:45:00\\t  00010030075657(电能表)\\t   7.2  0.1536   \n", "1  杭州富阳永顺工贸有限公司 2022-08-01  23:30:00\\t  00010030075657(电能表)\\t   6.0  0.1551   \n", "2  杭州富阳永顺工贸有限公司 2022-08-01  23:15:00\\t  00010030075657(电能表)\\t   6.3  0.1461   \n", "3  杭州富阳永顺工贸有限公司 2022-08-01  23:00:00\\t  00010030075657(电能表)\\t   6.9  0.1505   \n", "4  杭州富阳永顺工贸有限公司 2022-08-01  22:45:00\\t  00010030075657(电能表)\\t   7.5  0.0984   \n", "5  杭州富阳永顺工贸有限公司 2022-08-01  22:30:00\\t  00010030075657(电能表)\\t   6.9  0.0586   \n", "6  杭州富阳永顺工贸有限公司 2022-08-01  22:15:00\\t  00010030075657(电能表)\\t   6.6  0.0693   \n", "7  杭州富阳永顺工贸有限公司 2022-08-01  22:00:00\\t  00010030075657(电能表)\\t   8.1  0.0687   \n", "8  杭州富阳永顺工贸有限公司 2022-08-01  21:45:00\\t  00010030075657(电能表)\\t   8.1  0.0687   \n", "9  杭州富阳永顺工贸有限公司 2022-08-01  21:30:00\\t  00010030075657(电能表)\\t   7.8  0.1597   \n", "\n", "                    日期.1       户名  \n", "0  2022-01-21 13:15:00\\t  XXX有限公司  \n", "1  2022-01-21 13:00:00\\t  XXX有限公司  \n", "2  2022-01-21 12:45:00\\t  XXX有限公司  \n", "3  2022-01-21 12:30:00\\t  XXX有限公司  \n", "4  2022-01-21 12:15:00\\t  XXX有限公司  \n", "5  2022-01-21 12:00:00\\t  XXX有限公司  \n", "6  2022-01-21 11:45:00\\t  XXX有限公司  \n", "7  2022-01-21 11:30:00\\t  XXX有限公司  \n", "8  2022-01-21 11:15:00\\t  XXX有限公司  \n", "9  2022-01-21 11:00:00\\t  XXX有限公司  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head(10)"]}, {"cell_type": "code", "execution_count": 4, "id": "a7828336", "metadata": {}, "outputs": [], "source": ["def calculate_power_by_timeperiod(df, system_capacity, power_limit=None):\n", "    \"\"\"\n", "    处理DataFrame格式的功率数据，按时间段应用不同的计算规则\n", "    \n", "    参数:\n", "    df: DataFrame - 包含用户名称、日期、时间、资产编号、瞬时有功等列\n", "    system_capacity: int - 系统容量\n", "    power_limit: float - 功率上限，如果未提供则使用系统容量\n", "    \n", "    返回:\n", "    DataFrame - 月份、日期和96个时间点的功率值\n", "    \"\"\"\n", "    if power_limit is None:\n", "        power_limit = system_capacity\n", "    \n", "    # 数据预处理\n", "    df_clean = df.copy()\n", "    \n", "    # 清理时间列中的制表符\n", "    df_clean['Unnamed: 2'] = df_clean['Unnamed: 2'].astype(str).str.replace('\\t', '')\n", "    \n", "    # 合并日期和时间列，创建完整的datetime\n", "    df_clean['datetime'] = pd.to_datetime(df_clean['日期'].astype(str) + ' ' + df_clean['Unnamed: 2'])\n", "    \n", "    # 提取时间部分用于判断时间段\n", "    df_clean['time'] = df_clean['datetime'].dt.time\n", "    \n", "    # 定义特殊时间段\n", "    def is_special_period(t):\n", "        return ((t >= pd.Timestamp('00:00:00').time() and t <= pd.Timestamp('07:45:00').time()) or\n", "                (t >= pd.Timestamp('11:00:00').time() and t <= pd.Timestamp('12:45:00').time()) or\n", "                (t >= pd.Timestamp('22:00:00').time() and t <= pd.Timestamp('23:45:00').time()))\n", "    \n", "    # 应用计算规则\n", "    def calculate_processed_power(row):\n", "        original_power = row['瞬时有功']\n", "        t = row['time']\n", "        \n", "        if is_special_period(t):\n", "            # 特殊时段: min[max(功率上限-原有功功率值,0),系统容量]\n", "            return min(max(power_limit - original_power, 0), system_capacity)\n", "        else:\n", "            # 其他时段: min[max(原有功功率值,0),系统容量]\n", "            return min(max(original_power, 0), system_capacity)\n", "    \n", "    df_clean['processed_power'] = df_clean.apply(calculate_processed_power, axis=1)\n", "    \n", "    # 生成96个时间点列名\n", "    time_columns = []\n", "    for hour in range(24):\n", "        for minute in [0, 15, 30, 45]:\n", "            time_columns.append(f\"{hour:02d}:{minute:02d}:00\")\n", "    \n", "    # 按日期分组并重塑数据\n", "    df_clean['date'] = df_clean['datetime'].dt.date\n", "    df_clean['month'] = df_clean['datetime'].dt.month\n", "    df_clean['time_str'] = df_clean['datetime'].dt.strftime('%H:%M:%S')\n", "    \n", "    # 创建透视表\n", "    pivot_df = df_clean.pivot_table(\n", "        index=['month', 'date'], \n", "        columns='time_str', \n", "        values='processed_power',\n", "        aggfunc='mean'\n", "    ).reset_index()\n", "    \n", "    # 确保所有96个时间点都存在\n", "    for col in time_columns:\n", "        if col not in pivot_df.columns:\n", "            pivot_df[col] = np.nan\n", "    \n", "    # 重新排序列\n", "    result_columns = ['month', 'date'] + time_columns\n", "    pivot_df = pivot_df[result_columns]\n", "    \n", "    return pivot_df"]}, {"cell_type": "code", "execution_count": 5, "id": "41d100af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>time_str</th>\n", "      <th>month</th>\n", "      <th>date</th>\n", "      <th>00:00:00</th>\n", "      <th>00:15:00</th>\n", "      <th>00:30:00</th>\n", "      <th>00:45:00</th>\n", "      <th>01:00:00</th>\n", "      <th>01:15:00</th>\n", "      <th>01:30:00</th>\n", "      <th>01:45:00</th>\n", "      <th>...</th>\n", "      <th>21:30:00</th>\n", "      <th>21:45:00</th>\n", "      <th>22:00:00</th>\n", "      <th>22:15:00</th>\n", "      <th>22:30:00</th>\n", "      <th>22:45:00</th>\n", "      <th>23:00:00</th>\n", "      <th>23:15:00</th>\n", "      <th>23:30:00</th>\n", "      <th>23:45:00</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023-01-01</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.4</td>\n", "      <td>5.1</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>2023-01-02</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.4</td>\n", "      <td>5.4</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>2023-01-03</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.4</td>\n", "      <td>5.4</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>2023-01-04</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.7</td>\n", "      <td>5.7</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>2023-01-05</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>21.9</td>\n", "      <td>21.9</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>341</th>\n", "      <td>12</td>\n", "      <td>2022-12-27</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.7</td>\n", "      <td>5.7</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>342</th>\n", "      <td>12</td>\n", "      <td>2022-12-28</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.1</td>\n", "      <td>5.1</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>343</th>\n", "      <td>12</td>\n", "      <td>2022-12-29</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.4</td>\n", "      <td>5.4</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>344</th>\n", "      <td>12</td>\n", "      <td>2022-12-30</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.1</td>\n", "      <td>5.1</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>345</th>\n", "      <td>12</td>\n", "      <td>2022-12-31</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>5.4</td>\n", "      <td>5.4</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>346 rows × 98 columns</p>\n", "</div>"], "text/plain": ["time_str  month        date  00:00:00  00:15:00  00:30:00  00:45:00  01:00:00  \\\n", "0             1  2023-01-01     200.0     200.0     200.0     200.0     200.0   \n", "1             1  2023-01-02     200.0     200.0     200.0     200.0     200.0   \n", "2             1  2023-01-03     200.0     200.0     200.0     200.0     200.0   \n", "3             1  2023-01-04     200.0     200.0     200.0     200.0     200.0   \n", "4             1  2023-01-05     200.0     200.0     200.0     200.0     200.0   \n", "..          ...         ...       ...       ...       ...       ...       ...   \n", "341          12  2022-12-27     200.0     200.0     200.0     200.0     200.0   \n", "342          12  2022-12-28     200.0     200.0     200.0     200.0     200.0   \n", "343          12  2022-12-29     200.0     200.0     200.0     200.0     200.0   \n", "344          12  2022-12-30     200.0     200.0     200.0     200.0     200.0   \n", "345          12  2022-12-31     200.0     200.0     200.0     200.0     200.0   \n", "\n", "time_str  01:15:00  01:30:00  01:45:00  ...  21:30:00  21:45:00  22:00:00  \\\n", "0            200.0     200.0     200.0  ...       5.4       5.1     200.0   \n", "1            200.0     200.0     200.0  ...       5.4       5.4     200.0   \n", "2            200.0     200.0     200.0  ...       5.4       5.4     200.0   \n", "3            200.0     200.0     200.0  ...       5.7       5.7     200.0   \n", "4            200.0     200.0     200.0  ...      21.9      21.9     200.0   \n", "..             ...       ...       ...  ...       ...       ...       ...   \n", "341          200.0     200.0     200.0  ...       5.7       5.7     200.0   \n", "342          200.0     200.0     200.0  ...       5.1       5.1     200.0   \n", "343          200.0     200.0     200.0  ...       5.4       5.4     200.0   \n", "344          200.0     200.0     200.0  ...       5.1       5.1     200.0   \n", "345          200.0     200.0     200.0  ...       5.4       5.4     200.0   \n", "\n", "time_str  22:15:00  22:30:00  22:45:00  23:00:00  23:15:00  23:30:00  23:45:00  \n", "0            200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "1            200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "2            200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "3            200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "4            200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "..             ...       ...       ...       ...       ...       ...       ...  \n", "341          200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "342          200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "343          200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "344          200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "345          200.0     200.0     200.0     200.0     200.0     200.0     200.0  \n", "\n", "[346 rows x 98 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result= calculate_power_by_timeperiod(data, 200, 920)\n", "result"]}, {"cell_type": "code", "execution_count": null, "id": "d04e0e20", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}