import pandas as pd
import numpy as np

data = pd.read_excel('./data/(厂商机密)储能容量测算模型.xlsx',sheet_name='负荷-原始清单')
data.dropna(axis=1, how='all', inplace=True)
data.iloc[:,:-3]


data.head(10)

def calculate_power_by_timeperiod(df, system_capacity, power_limit=None):
    """
    处理DataFrame格式的功率数据，按时间段应用不同的计算规则
    
    参数:
    df: DataFrame - 包含用户名称、日期、时间、资产编号、瞬时有功等列
    system_capacity: int - 系统容量
    power_limit: float - 功率上限，如果未提供则使用系统容量
    
    返回:
    DataFrame - 月份、日期和96个时间点的功率值
    """
    if power_limit is None:
        power_limit = system_capacity
    
    # 数据预处理
    df_clean = df.copy()
    
    # 清理时间列中的制表符
    df_clean['Unnamed: 2'] = df_clean['Unnamed: 2'].astype(str).str.replace('\t', '')
    
    # 合并日期和时间列，创建完整的datetime
    df_clean['datetime'] = pd.to_datetime(df_clean['日期'].astype(str) + ' ' + df_clean['Unnamed: 2'])
    
    # 提取时间部分用于判断时间段
    df_clean['time'] = df_clean['datetime'].dt.time
    
    # 定义特殊时间段
    def is_special_period(t):
        return ((t >= pd.Timestamp('00:00:00').time() and t <= pd.Timestamp('07:45:00').time()) or
                (t >= pd.Timestamp('11:00:00').time() and t <= pd.Timestamp('12:45:00').time()) or
                (t >= pd.Timestamp('22:00:00').time() and t <= pd.Timestamp('23:45:00').time()))
    
    # 应用计算规则
    def calculate_processed_power(row):
        original_power = row['瞬时有功']
        t = row['time']
        
        if is_special_period(t):
            # 特殊时段: min[max(功率上限-原有功功率值,0),系统容量]
            return min(max(power_limit - original_power, 0), system_capacity)
        else:
            # 其他时段: min[max(原有功功率值,0),系统容量]
            return min(max(original_power, 0), system_capacity)
    
    df_clean['processed_power'] = df_clean.apply(calculate_processed_power, axis=1)
    
    # 生成96个时间点列名
    time_columns = []
    for hour in range(24):
        for minute in [0, 15, 30, 45]:
            time_columns.append(f"{hour:02d}:{minute:02d}:00")
    
    # 按日期分组并重塑数据
    df_clean['date'] = df_clean['datetime'].dt.date
    df_clean['month'] = df_clean['datetime'].dt.month
    df_clean['time_str'] = df_clean['datetime'].dt.strftime('%H:%M:%S')
    
    # 创建透视表
    pivot_df = df_clean.pivot_table(
        index=['month', 'date'], 
        columns='time_str', 
        values='processed_power',
        aggfunc='mean'
    ).reset_index()
    
    # 确保所有96个时间点都存在
    for col in time_columns:
        if col not in pivot_df.columns:
            pivot_df[col] = np.nan
    
    # 重新排序列
    result_columns = ['month', 'date'] + time_columns
    pivot_df = pivot_df[result_columns]
    
    return pivot_df

result= calculate_power_by_timeperiod(data, 200, 920)
result

