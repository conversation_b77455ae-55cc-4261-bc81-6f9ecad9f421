#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
储能系统计算演示脚本
Energy Storage System Calculation Demo Script

本脚本演示如何使用储能系统计算模块进行完整的计算流程
"""

from 储能系统集成计算 import main_calculation_pipeline, EnergyStorageSystemCalculator
import pandas as pd
import os


def demo_basic_usage():
    """演示基本使用方法"""
    print("=== 基本使用演示 ===")
    
    # 输入文件路径
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print(f"错误: 找不到输入文件 {file_path}")
        return
    
    # 基本调用
    try:
        results = main_calculation_pipeline(
            file_path=file_path,
            output_file="基本使用结果.xlsx"
        )
        
        print("✓ 基本计算完成")
        print(f"  计算天数: {results['汇总指标结果']['计算天数']}")
        print(f"  总收益: {results['汇总指标结果']['总利润']:.2f} 元")
        
    except Exception as e:
        print(f"✗ 基本计算失败: {str(e)}")


def demo_custom_parameters():
    """演示自定义参数使用"""
    print("\n=== 自定义参数演示 ===")
    
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print(f"错误: 找不到输入文件 {file_path}")
        return
    
    # 自定义参数
    custom_config = {
        'system_capacity_kw': 300,          # 增加系统容量
        'system_energy_kwh': 600,           # 增加系统电量
        'transformer_capacity_kva': 1500,   # 增加变压器容量
        'power_factor': 0.95,               # 提高功率因素
        'dod_percentage': 0.90,             # 降低DOD
        'peak1_discharge_rate': 0.6,        # 提高峰1放电倍率
    }
    
    try:
        results = main_calculation_pipeline(
            file_path=file_path,
            config_params=custom_config,
            output_file="自定义参数结果.xlsx"
        )
        
        print("✓ 自定义参数计算完成")
        print("使用的参数:")
        for key, value in custom_config.items():
            print(f"  {key}: {value}")
        
        print("计算结果:")
        print(f"  计算天数: {results['汇总指标结果']['计算天数']}")
        print(f"  总收益: {results['汇总指标结果']['总利润']:.2f} 元")
        print(f"  全年电量: {results['汇总指标结果']['全年电量_万kwh']:.2f} 万kWh")
        
    except Exception as e:
        print(f"✗ 自定义参数计算失败: {str(e)}")


def demo_data_platform_interface():
    """演示数据平台接口使用"""
    print("\n=== 数据平台接口演示 ===")
    
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print(f"错误: 找不到输入文件 {file_path}")
        return
    
    try:
        # 模拟从数据平台获取数据
        print("正在从Excel文件模拟数据平台数据...")
        load_data = pd.read_excel(file_path, sheet_name='负荷-原始清单')
        price_data = pd.read_excel(file_path, sheet_name='基础参数-电价')
        
        print(f"负荷数据: {load_data.shape}")
        print(f"电价数据: {price_data.shape}")
        
        # 使用数据平台接口
        calculator = EnergyStorageSystemCalculator()
        
        config_params = {
            'system_capacity_kw': 250,
            'system_energy_kwh': 500,
        }
        
        results = calculator.calculate_from_data_platform(
            load_data=load_data,
            price_data=price_data,
            config_params=config_params
        )
        
        # 导出结果
        calculator.export_results_to_excel(results, "数据平台接口结果.xlsx")
        
        print("✓ 数据平台接口计算完成")
        print(f"  计算天数: {results['汇总指标结果']['计算天数']}")
        print(f"  总收益: {results['汇总指标结果']['总利润']:.2f} 元")
        
    except Exception as e:
        print(f"✗ 数据平台接口计算失败: {str(e)}")


def demo_output_analysis():
    """演示输出结果分析"""
    print("\n=== 输出结果分析演示 ===")
    
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print(f"错误: 找不到输入文件 {file_path}")
        return
    
    try:
        results = main_calculation_pipeline(
            file_path=file_path,
            output_file="分析演示结果.xlsx"
        )
        
        # 分析三个输出表
        output_tables = results['输出表']
        
        print("✓ 三个输出表分析:")
        
        # 1. 装机容量和基本电费汇总表
        table1 = output_tables['装机容量和基本电费汇总表']
        print(f"\n1. 装机容量和基本电费汇总表 ({table1.shape[0]} 行)")
        capacity_indicators = table1[table1['指标类别'] == '装机容量']
        print("   装机容量指标:")
        for _, row in capacity_indicators.head(3).iterrows():
            print(f"     {row['指标名称']}: {row['数值']} {row['单位']}")
        
        # 2. 每日详细计算结果表
        table2 = output_tables['每日详细计算结果表']
        print(f"\n2. 每日详细计算结果表 ({table2.shape[0]} 行, {table2.shape[1]} 列)")
        print(f"   数据时间范围: {table2['日期'].min()} 至 {table2['日期'].max()}")
        print(f"   平均日收益: {table2['总收益'].mean():.4f} 元")
        print(f"   最大日收益: {table2['总收益'].max():.4f} 元")
        
        # 3. 年度汇总指标表
        table3 = output_tables['年度汇总指标表']
        print(f"\n3. 年度汇总指标表 ({table3.shape[0]} 行)")
        key_indicators = ['计算天数', '全年电量_万kwh', '总利润', '尖放电占比']
        for indicator in key_indicators:
            row = table3[table3['指标名称'] == indicator]
            if not row.empty:
                value = row.iloc[0]['数值']
                unit = row.iloc[0]['单位']
                print(f"   {indicator}: {value} {unit}")
        
        # 月度统计
        summary_results = results['汇总指标结果']
        monthly_stats = summary_results.get('月度统计', {})
        if monthly_stats:
            print(f"\n月度统计 (共 {len(monthly_stats)} 个月):")
            for month, stats in list(monthly_stats.items())[:3]:  # 显示前3个月
                print(f"   {month}月: {stats['天数']} 天, 收益 {stats['总收益']:.2f} 元")
        
    except Exception as e:
        print(f"✗ 输出结果分析失败: {str(e)}")


def demo_scenario_comparison():
    """演示场景对比分析"""
    print("\n=== 场景对比分析演示 ===")
    
    file_path = "data/(厂商机密)储能容量测算模型.xlsx"
    
    if not os.path.exists(file_path):
        print(f"错误: 找不到输入文件 {file_path}")
        return
    
    # 定义不同场景
    scenarios = {
        '基准场景': {
            'system_capacity_kw': 200,
            'system_energy_kwh': 400,
        },
        '高容量场景': {
            'system_capacity_kw': 300,
            'system_energy_kwh': 600,
        },
        '保守场景': {
            'system_capacity_kw': 200,
            'system_energy_kwh': 400,
            'dod_percentage': 0.80,
        },
        '激进场景': {
            'system_capacity_kw': 200,
            'system_energy_kwh': 400,
            'dod_percentage': 0.95,
            'peak1_discharge_rate': 0.8,
        }
    }
    
    comparison_results = {}
    
    try:
        for scenario_name, config in scenarios.items():
            print(f"正在计算 {scenario_name}...")
            
            results = main_calculation_pipeline(
                file_path=file_path,
                config_params=config,
                output_file=f"{scenario_name}_结果.xlsx"
            )
            
            summary = results['汇总指标结果']
            comparison_results[scenario_name] = {
                '总收益': summary['总利润'],
                '平均日收益': summary['平均日收益'],
                '全年电量': summary['全年电量_万kwh'],
                '尖放电占比': summary['尖放电占比'],
            }
        
        # 显示对比结果
        print("\n✓ 场景对比结果:")
        print(f"{'场景':<12} {'总收益(元)':<12} {'平均日收益(元)':<15} {'全年电量(万kWh)':<15} {'尖放电占比':<12}")
        print("-" * 70)
        
        for scenario, metrics in comparison_results.items():
            print(f"{scenario:<12} {metrics['总收益']:<12.2f} {metrics['平均日收益']:<15.4f} "
                  f"{metrics['全年电量']:<15.2f} {metrics['尖放电占比']:<12.2%}")
        
    except Exception as e:
        print(f"✗ 场景对比分析失败: {str(e)}")


def main():
    """主演示函数"""
    print("储能系统计算模块使用演示")
    print("=" * 50)
    
    # 运行各种演示
    demo_basic_usage()
    demo_custom_parameters()
    demo_data_platform_interface()
    demo_output_analysis()
    demo_scenario_comparison()
    
    print("\n" + "=" * 50)
    print("演示完成! 请查看生成的Excel文件了解详细结果。")


if __name__ == "__main__":
    main()
